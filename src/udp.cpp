#include "udp.h"
#include <cstring>
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <thread>
#include <chrono>

// UdpHeader implementation
UdpHeader::UdpHeader() : source_port(0), destination_port(0), length(0), checksum(0) {}

UdpHeader::UdpHeader(uint16_t src_port, uint16_t dst_port, uint16_t payload_length)
    : source_port(src_port), destination_port(dst_port),
      length(UDP_HEADER_SIZE + payload_length), checksum(0) {}

void UdpHeader::calculate_checksum(const uint8_t* payload, size_t payload_size,
                                  const IpAddress& src_ip, const IpAddress& dst_ip) {
    checksum = 0;

    // Create UDP header + payload buffer
    size_t udp_size = UDP_HEADER_SIZE + payload_size;
    std::vector<uint8_t> buffer(udp_size);

    // UDP header
    serialize(buffer.data());

    // Payload
    if (payload && payload_size > 0) {
        std::memcpy(buffer.data() + UDP_HEADER_SIZE, payload, payload_size);
    }

    // Calculate checksum using the utility function
    checksum = calculate_udp_checksum(buffer.data(), udp_size,
                                     src_ip.to_network_order(), dst_ip.to_network_order());
}

bool UdpHeader::verify_checksum(const uint8_t* payload, size_t payload_size,
                                const IpAddress& src_ip, const IpAddress& dst_ip) const {
    uint16_t saved_checksum = checksum;
    const_cast<UdpHeader*>(this)->checksum = 0;

    // Create UDP header + payload buffer
    size_t udp_size = UDP_HEADER_SIZE + payload_size;
    std::vector<uint8_t> buffer(udp_size);

    // UDP header
    const_cast<UdpHeader*>(this)->serialize(buffer.data());

    // Payload
    if (payload && payload_size > 0) {
        std::memcpy(buffer.data() + UDP_HEADER_SIZE, payload, payload_size);
    }

    uint16_t calculated = calculate_udp_checksum(buffer.data(), udp_size,
                                                src_ip.to_network_order(), dst_ip.to_network_order());
    const_cast<UdpHeader*>(this)->checksum = saved_checksum;

    return calculated == saved_checksum;
}

void UdpHeader::serialize(uint8_t* buffer) const {
    *reinterpret_cast<uint16_t*>(buffer) = htons_custom(source_port);
    *reinterpret_cast<uint16_t*>(buffer + 2) = htons_custom(destination_port);
    *reinterpret_cast<uint16_t*>(buffer + 4) = htons_custom(length);
    *reinterpret_cast<uint16_t*>(buffer + 6) = htons_custom(checksum);
}

UdpHeader UdpHeader::deserialize(const uint8_t* buffer) {
    UdpHeader header;
    header.source_port = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer));
    header.destination_port = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 2));
    header.length = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 4));
    header.checksum = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 6));
    return header;
}

bool UdpHeader::is_valid() const {
    return length >= UDP_HEADER_SIZE && length <= 65535;
}

std::string UdpHeader::to_string() const {
    std::ostringstream oss;
    oss << "UDP " << source_port << " -> " << destination_port 
        << " (len: " << length << ")";
    return oss.str();
}

// UdpDatagram implementation
UdpDatagram::UdpDatagram() {}

UdpDatagram::UdpDatagram(uint16_t src_port, uint16_t dst_port,
                         const uint8_t* payload, size_t payload_size)
    : header_(src_port, dst_port, payload_size) {
    if (payload && payload_size > 0) {
        set_payload(payload, payload_size);
    }
}

UdpDatagram::UdpDatagram(const UdpHeader& header, const uint8_t* payload, size_t payload_size)
    : header_(header) {
    if (payload && payload_size > 0) {
        set_payload(payload, payload_size);
    }
}

void UdpDatagram::set_payload(const uint8_t* data, size_t size) {
    payload_.resize(size);
    if (data && size > 0) {
        std::memcpy(payload_.data(), data, size);
    }
    header_.length = UDP_HEADER_SIZE + size;
}

std::vector<uint8_t> UdpDatagram::serialize(const IpAddress& src_ip, const IpAddress& dst_ip) const {
    std::vector<uint8_t> packet(UDP_HEADER_SIZE + payload_.size());

    // Calculate checksum
    UdpHeader header_copy = header_;
    header_copy.calculate_checksum(payload_.data(), payload_.size(), src_ip, dst_ip);

    // Serialize header
    header_copy.serialize(packet.data());

    // Copy payload
    if (!payload_.empty()) {
        std::memcpy(packet.data() + UDP_HEADER_SIZE, payload_.data(), payload_.size());
    }

    return packet;
}

bool UdpDatagram::deserialize(const uint8_t* data, size_t size,
                             const IpAddress& src_ip, const IpAddress& dst_ip) {
    if (size < UDP_HEADER_SIZE) {
        return false;
    }

    // Deserialize header
    header_ = UdpHeader::deserialize(data);

    if (!header_.is_valid() || header_.length > size) {
        return false;
    }

    // Extract payload
    size_t payload_size = header_.get_payload_length();
    if (payload_size > 0) {
        payload_.resize(payload_size);
        std::memcpy(payload_.data(), data + UDP_HEADER_SIZE, payload_size);
    } else {
        payload_.clear();
    }

    return true;
}

bool UdpDatagram::is_valid(const IpAddress& src_ip, const IpAddress& dst_ip) const {
    return header_.is_valid() && header_.verify_checksum(payload_.data(), payload_.size(), src_ip, dst_ip);
}

std::string UdpDatagram::to_string() const {
    std::ostringstream oss;
    oss << header_.to_string() << " (payload: " << payload_.size() << " bytes)";
    return oss.str();
}

void UdpDatagram::dump() const {
    std::cout << to_string() << std::endl;
    if (!payload_.empty()) {
        hex_dump(payload_.data(), payload_.size(), "  ");
    }
}

// UdpEndpoint implementation
UdpEndpoint::UdpEndpoint() : port(0) {}

UdpEndpoint::UdpEndpoint(const IpAddress& addr, uint16_t p) : ip(addr), port(p) {}

UdpEndpoint::UdpEndpoint(const std::string& ip_str, uint16_t p) : ip(ip_str), port(p) {}

bool UdpEndpoint::operator==(const UdpEndpoint& other) const {
    return ip == other.ip && port == other.port;
}

bool UdpEndpoint::operator!=(const UdpEndpoint& other) const {
    return !(*this == other);
}

bool UdpEndpoint::operator<(const UdpEndpoint& other) const {
    if (ip < other.ip) return true;
    if (other.ip < ip) return false;
    return port < other.port;
}

std::string UdpEndpoint::to_string() const {
    return ip.to_string() + ":" + std::to_string(port);
}

bool UdpEndpoint::is_valid() const {
    return port > 0; // IP address validation would need to be implemented
}

// UdpSocket implementation
UdpSocket::UdpSocket()
    : bound_(false), connected_(false), blocking_(true),
      max_receive_buffer_size_(1024), receive_timeout_(std::chrono::milliseconds(1000)) {}

UdpSocket::~UdpSocket() {
    close();
}

bool UdpSocket::bind(uint16_t port) {
    local_endpoint_ = UdpEndpoint(IpAddress("0.0.0.0"), port);
    bound_ = true;
    return true;
}

bool UdpSocket::bind(const IpAddress& ip, uint16_t port) {
    local_endpoint_ = UdpEndpoint(ip, port);
    bound_ = true;
    return true;
}

bool UdpSocket::connect(const UdpEndpoint& remote) {
    remote_endpoint_ = remote;
    connected_ = true;
    return true;
}

void UdpSocket::close() {
    std::lock_guard<std::mutex> lock(receive_mutex_);
    bound_ = false;
    connected_ = false;
    local_endpoint_ = UdpEndpoint();
    remote_endpoint_ = UdpEndpoint();

    // Clear receive buffer
    while (!receive_buffer_.empty()) {
        receive_buffer_.pop();
    }
}

ssize_t UdpSocket::send(const uint8_t* data, size_t size) {
    if (!connected_) {
        return -1;
    }
    return send_to(data, size, remote_endpoint_);
}

ssize_t UdpSocket::send_to(const uint8_t* data, size_t size, const UdpEndpoint& destination) {
    if (!bound_) {
        return -1;
    }

    // Create UDP datagram
    UdpDatagram datagram(local_endpoint_.port, destination.port, data, size);

    // Update statistics
    stats_.datagrams_sent++;
    stats_.bytes_sent += size;

    return size;
}

ssize_t UdpSocket::receive(uint8_t* buffer, size_t buffer_size) {
    UdpEndpoint source;
    return receive_from(buffer, buffer_size, source);
}

ssize_t UdpSocket::receive_from(uint8_t* buffer, size_t buffer_size, UdpEndpoint& source) {
    if (!bound_) {
        return -1;
    }

    std::unique_lock<std::mutex> lock(receive_mutex_);

    // Wait for data or timeout
    if (blocking_) {
        if (!receive_cv_.wait_for(lock, receive_timeout_, [this] { return !receive_buffer_.empty(); })) {
            return -1; // Timeout
        }
    } else if (receive_buffer_.empty()) {
        return -1; // No data available
    }

    auto& received = receive_buffer_.front();
    source = received.source;

    size_t copy_size = std::min(buffer_size, received.datagram.payload_size());
    std::memcpy(buffer, received.datagram.payload(), copy_size);

    receive_buffer_.pop();

    // Update statistics
    stats_.datagrams_received++;
    stats_.bytes_received += copy_size;

    return copy_size;
}

size_t UdpSocket::get_pending_data_size() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(receive_mutex_));
    return receive_buffer_.size();
}

void UdpSocket::receive_datagram(const UdpDatagram& datagram, const UdpEndpoint& source) {
    std::lock_guard<std::mutex> lock(receive_mutex_);

    if (receive_buffer_.size() >= max_receive_buffer_size_) {
        // Buffer full, drop oldest datagram
        receive_buffer_.pop();
    }

    ReceivedDatagram received;
    received.datagram = datagram;
    received.source = source;
    received.timestamp = std::chrono::steady_clock::now();

    receive_buffer_.push(received);
    receive_cv_.notify_one();
}

// UdpProtocol implementation
UdpProtocol::UdpProtocol(Ipv4Protocol& ipv4) : ipv4_(ipv4) {}

UdpProtocol::~UdpProtocol() = default;

std::shared_ptr<UdpSocket> UdpProtocol::create_socket() {
    return std::make_shared<UdpSocket>();
}

// Utility functions
bool is_valid_udp_datagram(const uint8_t* data, size_t size) {
    if (size < UDP_HEADER_SIZE) {
        return false;
    }

    UdpDatagram datagram;
    IpAddress dummy_src("0.0.0.0"), dummy_dst("0.0.0.0");
    return datagram.deserialize(data, size, dummy_src, dummy_dst);
}

std::string udp_port_to_string(uint16_t port) {
    // Common well-known ports
    switch (port) {
        case 53: return "DNS";
        case 67: return "DHCP Server";
        case 68: return "DHCP Client";
        case 69: return "TFTP";
        case 123: return "NTP";
        case 161: return "SNMP";
        case 162: return "SNMP Trap";
        case 514: return "Syslog";
        default: {
            std::ostringstream oss;
            oss << "Port " << port;
            return oss.str();
        }
    }
}
