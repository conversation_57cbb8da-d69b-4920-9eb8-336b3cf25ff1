#include "ipv4.h"
#include <cstring>
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <thread>
#include <mutex>

// Ipv4Header implementation
Ipv4Header::Ipv4Header() 
    : version_ihl(0x45), type_of_service(0), total_length(0), identification(0),
      flags_fragment(0), time_to_live(IPV4_DEFAULT_TTL), protocol(0), 
      header_checksum(0) {
    set_version(IPV4_VERSION);
    set_ihl(5); // 20 bytes = 5 * 4
}

Ipv4Header::Ipv4Header(const IpAddress& src, const IpAddress& dst, uint8_t proto, 
                       uint16_t payload_length, uint8_t ttl)
    : version_ihl(0x45), type_of_service(0), identification(random_uint16()),
      flags_fragment(0), time_to_live(ttl), protocol(proto), header_checksum(0),
      source_ip(src), destination_ip(dst) {
    
    set_version(IPV4_VERSION);
    set_ihl(5); // 20 bytes = 5 * 4
    total_length = get_header_length() + payload_length;
}

void Ipv4Header::calculate_checksum() {
    header_checksum = 0;
    header_checksum = calculate_ip_checksum(this, get_header_length());
}

bool Ipv4Header::verify_checksum() const {
    uint16_t saved_checksum = header_checksum;
    const_cast<Ipv4Header*>(this)->header_checksum = 0;
    uint16_t calculated = calculate_ip_checksum(this, get_header_length());
    const_cast<Ipv4Header*>(this)->header_checksum = saved_checksum;
    return calculated == saved_checksum;
}

void Ipv4Header::serialize(uint8_t* buffer) const {
    uint8_t* ptr = buffer;
    
    *ptr++ = version_ihl;
    *ptr++ = type_of_service;
    *reinterpret_cast<uint16_t*>(ptr) = htons_custom(total_length);
    ptr += 2;
    *reinterpret_cast<uint16_t*>(ptr) = htons_custom(identification);
    ptr += 2;
    *reinterpret_cast<uint16_t*>(ptr) = htons_custom(flags_fragment);
    ptr += 2;
    *ptr++ = time_to_live;
    *ptr++ = protocol;
    *reinterpret_cast<uint16_t*>(ptr) = htons_custom(header_checksum);
    ptr += 2;
    *reinterpret_cast<uint32_t*>(ptr) = source_ip.to_network_order();
    ptr += 4;
    *reinterpret_cast<uint32_t*>(ptr) = destination_ip.to_network_order();
}

Ipv4Header Ipv4Header::deserialize(const uint8_t* buffer) {
    Ipv4Header header;
    const uint8_t* ptr = buffer;
    
    header.version_ihl = *ptr++;
    header.type_of_service = *ptr++;
    header.total_length = ntohs_custom(*reinterpret_cast<const uint16_t*>(ptr));
    ptr += 2;
    header.identification = ntohs_custom(*reinterpret_cast<const uint16_t*>(ptr));
    ptr += 2;
    header.flags_fragment = ntohs_custom(*reinterpret_cast<const uint16_t*>(ptr));
    ptr += 2;
    header.time_to_live = *ptr++;
    header.protocol = *ptr++;
    header.header_checksum = ntohs_custom(*reinterpret_cast<const uint16_t*>(ptr));
    ptr += 2;
    header.source_ip = IpAddress::from_network_order(*reinterpret_cast<const uint32_t*>(ptr));
    ptr += 4;
    header.destination_ip = IpAddress::from_network_order(*reinterpret_cast<const uint32_t*>(ptr));
    
    return header;
}

bool Ipv4Header::is_valid() const {
    return get_version() == IPV4_VERSION &&
           get_ihl() >= 5 &&
           get_header_length() >= IPV4_MIN_HEADER_SIZE &&
           get_header_length() <= IPV4_MAX_HEADER_SIZE &&
           total_length >= get_header_length();
}

bool Ipv4Header::is_fragmented() const {
    return has_more_fragments() || get_fragment_offset() > 0;
}

bool Ipv4Header::has_more_fragments() const {
    return (flags_fragment & IPV4_FLAG_MORE_FRAGMENTS) != 0;
}

bool Ipv4Header::dont_fragment() const {
    return (flags_fragment & IPV4_FLAG_DONT_FRAGMENT) != 0;
}

std::string Ipv4Header::to_string() const {
    std::ostringstream oss;
    oss << "IPv4: " << source_ip.to_string() << " -> " << destination_ip.to_string()
        << " (proto: " << static_cast<int>(protocol) << ", len: " << total_length
        << ", ttl: " << static_cast<int>(time_to_live) << ")";
    return oss.str();
}

// Ipv4Packet implementation
Ipv4Packet::Ipv4Packet() {}

Ipv4Packet::Ipv4Packet(const IpAddress& src, const IpAddress& dst, uint8_t protocol,
                       const uint8_t* payload, size_t payload_size, uint8_t ttl)
    : header_(src, dst, protocol, payload_size, ttl) {
    if (payload && payload_size > 0) {
        set_payload(payload, payload_size);
    }
}

void Ipv4Packet::set_payload(const uint8_t* data, size_t size) {
    payload_.resize(size);
    if (data && size > 0) {
        std::memcpy(payload_.data(), data, size);
    }
    header_.total_length = header_.get_header_length() + size;
}

std::vector<uint8_t> Ipv4Packet::serialize() const {
    std::vector<uint8_t> packet(header_.get_header_length() + payload_.size());
    
    // Calculate checksum
    Ipv4Header header_copy = header_;
    header_copy.calculate_checksum();
    
    // Serialize header
    header_copy.serialize(packet.data());
    
    // Copy payload
    if (!payload_.empty()) {
        std::memcpy(packet.data() + header_.get_header_length(), payload_.data(), payload_.size());
    }
    
    return packet;
}

bool Ipv4Packet::deserialize(const uint8_t* data, size_t size) {
    if (size < IPV4_MIN_HEADER_SIZE) {
        return false;
    }
    
    // Deserialize header
    header_ = Ipv4Header::deserialize(data);
    
    if (!header_.is_valid() || header_.total_length > size) {
        return false;
    }
    
    // Extract payload
    size_t payload_size = header_.total_length - header_.get_header_length();
    if (payload_size > 0) {
        payload_.resize(payload_size);
        std::memcpy(payload_.data(), data + header_.get_header_length(), payload_size);
    } else {
        payload_.clear();
    }
    
    return true;
}

bool Ipv4Packet::is_valid() const {
    return header_.is_valid() && header_.verify_checksum();
}

std::string Ipv4Packet::to_string() const {
    std::ostringstream oss;
    oss << header_.to_string() << " (payload: " << payload_.size() << " bytes)";
    return oss.str();
}

void Ipv4Packet::dump() const {
    std::cout << to_string() << std::endl;
    if (!payload_.empty()) {
        hex_dump(payload_.data(), payload_.size(), "  ");
    }
}

// RouteEntry implementation
RouteEntry::RouteEntry() : metric(0) {}

RouteEntry::RouteEntry(const IpAddress& net, const IpAddress& mask, 
                       const IpAddress& gw, const std::string& iface, uint32_t met)
    : network(net), netmask(mask), gateway(gw), interface(iface), metric(met) {}

bool RouteEntry::matches(const IpAddress& destination) const {
    uint32_t dest_addr = ntohl_custom(destination.addr);
    uint32_t net_addr = ntohl_custom(network.addr);
    uint32_t mask_addr = ntohl_custom(netmask.addr);
    
    return (dest_addr & mask_addr) == (net_addr & mask_addr);
}

std::string RouteEntry::to_string() const {
    std::ostringstream oss;
    oss << network.to_string() << "/" << netmask.to_string() 
        << " via " << gateway.to_string() << " dev " << interface
        << " metric " << metric;
    return oss.str();
}

// RoutingTable implementation
RoutingTable::RoutingTable() {}

void RoutingTable::add_route(const RouteEntry& route) {
    std::lock_guard<std::mutex> lock(mutex_);
    routes_.push_back(route);
    
    // Sort by metric (lower metric = higher priority)
    std::sort(routes_.begin(), routes_.end(), 
              [](const RouteEntry& a, const RouteEntry& b) {
                  return a.metric < b.metric;
              });
}

bool RoutingTable::remove_route(const IpAddress& network, const IpAddress& netmask) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = std::find_if(routes_.begin(), routes_.end(),
                          [&](const RouteEntry& route) {
                              return route.network == network && route.netmask == netmask;
                          });
    
    if (it != routes_.end()) {
        routes_.erase(it);
        return true;
    }
    return false;
}

void RoutingTable::clear_routes() {
    std::lock_guard<std::mutex> lock(mutex_);
    routes_.clear();
}

bool RoutingTable::lookup_route(const IpAddress& destination, RouteEntry& route) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (const auto& r : routes_) {
        if (r.matches(destination)) {
            route = r;
            return true;
        }
    }
    return false;
}

IpAddress RoutingTable::get_next_hop(const IpAddress& destination) const {
    RouteEntry route;
    if (lookup_route(destination, route)) {
        return route.gateway.addr == 0 ? destination : route.gateway;
    }
    return IpAddress(); // No route found
}

void RoutingTable::set_default_route(const IpAddress& gateway, const std::string& interface) {
    RouteEntry default_route(IpAddress("0.0.0.0"), IpAddress("0.0.0.0"), 
                            gateway, interface, 100);
    add_route(default_route);
}

bool RoutingTable::has_default_route() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return std::any_of(routes_.begin(), routes_.end(),
                      [](const RouteEntry& route) {
                          return route.network.addr == 0 && route.netmask.addr == 0;
                      });
}

std::vector<RouteEntry> RoutingTable::get_all_routes() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return routes_;
}

size_t RoutingTable::size() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return routes_.size();
}

void RoutingTable::dump() const {
    std::cout << "Routing Table:" << std::endl;
    auto routes = get_all_routes();
    if (routes.empty()) {
        std::cout << "  (empty)" << std::endl;
        return;
    }
    
    for (const auto& route : routes) {
        std::cout << "  " << route.to_string() << std::endl;
    }
}

std::string RoutingTable::to_string() const {
    std::ostringstream oss;
    auto routes = get_all_routes();
    oss << "Routing Table (" << routes.size() << " entries):" << std::endl;
    for (const auto& route : routes) {
        oss << "  " << route.to_string() << std::endl;
    }
    return oss.str();
}

// Utility functions
std::string ipv4_protocol_to_string(uint8_t protocol) {
    switch (protocol) {
        case IPV4_PROTOCOL_ICMP: return "ICMP";
        case IPV4_PROTOCOL_TCP: return "TCP";
        case IPV4_PROTOCOL_UDP: return "UDP";
        default: {
            std::ostringstream oss;
            oss << "Protocol " << static_cast<int>(protocol);
            return oss.str();
        }
    }
}

bool is_valid_ipv4_packet(const uint8_t* data, size_t size) {
    if (size < IPV4_MIN_HEADER_SIZE) {
        return false;
    }
    
    Ipv4Header header = Ipv4Header::deserialize(data);
    return header.is_valid() && header.total_length <= size;
}

IpAddress calculate_network_address(const IpAddress& ip, const IpAddress& netmask) {
    uint32_t ip_addr = ntohl_custom(ip.addr);
    uint32_t mask_addr = ntohl_custom(netmask.addr);
    return IpAddress(htonl_custom(ip_addr & mask_addr));
}

IpAddress calculate_broadcast_address(const IpAddress& network, const IpAddress& netmask) {
    uint32_t net_addr = ntohl_custom(network.addr);
    uint32_t mask_addr = ntohl_custom(netmask.addr);
    return IpAddress(htonl_custom(net_addr | ~mask_addr));
}
