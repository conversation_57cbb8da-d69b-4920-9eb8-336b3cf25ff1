#include "arp.h"
#include <cstring>
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <thread>

// ArpHeader implementation
ArpHeader::ArpHeader() 
    : hardware_type(ARP_HARDWARE_ETHERNET), protocol_type(ARP_PROTOCOL_IPV4),
      hardware_size(ARP_HARDWARE_SIZE), protocol_size(ARP_PROTOCOL_SIZE),
      operation(0) {}

ArpHeader::ArpHeader(uint16_t op, const MacAddress& sender_mac, const IpAddress& sender_ip,
                     const MacAddress& target_mac, const IpAddress& target_ip)
    : hardware_type(ARP_HARDWARE_ETHERNET), protocol_type(ARP_PROTOCOL_IPV4),
      hardware_size(ARP_HARDWARE_SIZE), protocol_size(ARP_PROTOCOL_SIZE),
      operation(op), sender_mac(sender_mac), sender_ip(sender_ip),
      target_mac(target_mac), target_ip(target_ip) {}

void ArpHeader::serialize(uint8_t* buffer) const {
    uint8_t* ptr = buffer;
    
    *reinterpret_cast<uint16_t*>(ptr) = htons_custom(hardware_type);
    ptr += 2;
    *reinterpret_cast<uint16_t*>(ptr) = htons_custom(protocol_type);
    ptr += 2;
    *ptr++ = hardware_size;
    *ptr++ = protocol_size;
    *reinterpret_cast<uint16_t*>(ptr) = htons_custom(operation);
    ptr += 2;
    
    std::memcpy(ptr, sender_mac.addr, 6);
    ptr += 6;
    *reinterpret_cast<uint32_t*>(ptr) = sender_ip.to_network_order();
    ptr += 4;
    std::memcpy(ptr, target_mac.addr, 6);
    ptr += 6;
    *reinterpret_cast<uint32_t*>(ptr) = target_ip.to_network_order();
}

ArpHeader ArpHeader::deserialize(const uint8_t* buffer) {
    ArpHeader header;
    const uint8_t* ptr = buffer;
    
    header.hardware_type = ntohs_custom(*reinterpret_cast<const uint16_t*>(ptr));
    ptr += 2;
    header.protocol_type = ntohs_custom(*reinterpret_cast<const uint16_t*>(ptr));
    ptr += 2;
    header.hardware_size = *ptr++;
    header.protocol_size = *ptr++;
    header.operation = ntohs_custom(*reinterpret_cast<const uint16_t*>(ptr));
    ptr += 2;
    
    std::memcpy(header.sender_mac.addr, ptr, 6);
    ptr += 6;
    header.sender_ip = IpAddress::from_network_order(*reinterpret_cast<const uint32_t*>(ptr));
    ptr += 4;
    std::memcpy(header.target_mac.addr, ptr, 6);
    ptr += 6;
    header.target_ip = IpAddress::from_network_order(*reinterpret_cast<const uint32_t*>(ptr));
    
    return header;
}

bool ArpHeader::is_valid() const {
    return hardware_type == ARP_HARDWARE_ETHERNET &&
           protocol_type == ARP_PROTOCOL_IPV4 &&
           hardware_size == ARP_HARDWARE_SIZE &&
           protocol_size == ARP_PROTOCOL_SIZE &&
           (operation == ARP_OP_REQUEST || operation == ARP_OP_REPLY);
}

std::string ArpHeader::to_string() const {
    std::ostringstream oss;
    oss << "ARP " << arp_operation_to_string(operation) 
        << ": " << sender_ip.to_string() << " (" << sender_mac.to_string() << ")"
        << " -> " << target_ip.to_string() << " (" << target_mac.to_string() << ")";
    return oss.str();
}

// ArpEntry implementation
ArpEntry::ArpEntry() : is_static(false) {
    timestamp = std::chrono::steady_clock::now();
}

ArpEntry::ArpEntry(const MacAddress& mac, bool static_entry) 
    : mac_address(mac), is_static(static_entry) {
    timestamp = std::chrono::steady_clock::now();
}

bool ArpEntry::is_expired(std::chrono::seconds timeout) const {
    if (is_static) return false;
    
    auto now = std::chrono::steady_clock::now();
    return (now - timestamp) > timeout;
}

std::string ArpEntry::to_string() const {
    std::ostringstream oss;
    oss << mac_address.to_string();
    if (is_static) {
        oss << " (static)";
    } else {
        auto now = std::chrono::steady_clock::now();
        auto age = std::chrono::duration_cast<std::chrono::seconds>(now - timestamp);
        oss << " (age: " << age.count() << "s)";
    }
    return oss.str();
}

// ArpTable implementation
ArpTable::ArpTable() {}

void ArpTable::add_entry(const IpAddress& ip, const MacAddress& mac, bool is_static) {
    std::lock_guard<std::mutex> lock(mutex_);
    entries_[ip] = ArpEntry(mac, is_static);
}

bool ArpTable::remove_entry(const IpAddress& ip) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = entries_.find(ip);
    if (it != entries_.end()) {
        entries_.erase(it);
        return true;
    }
    return false;
}

void ArpTable::clear_dynamic_entries() {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = entries_.begin();
    while (it != entries_.end()) {
        if (!it->second.is_static) {
            it = entries_.erase(it);
        } else {
            ++it;
        }
    }
}

void ArpTable::clear_all_entries() {
    std::lock_guard<std::mutex> lock(mutex_);
    entries_.clear();
}

bool ArpTable::lookup(const IpAddress& ip, MacAddress& mac) const {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = entries_.find(ip);
    if (it != entries_.end() && !it->second.is_expired()) {
        mac = it->second.mac_address;
        return true;
    }
    return false;
}

bool ArpTable::has_entry(const IpAddress& ip) const {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = entries_.find(ip);
    return it != entries_.end() && !it->second.is_expired();
}

void ArpTable::cleanup_expired_entries(std::chrono::seconds timeout) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = entries_.begin();
    while (it != entries_.end()) {
        if (it->second.is_expired(timeout)) {
            it = entries_.erase(it);
        } else {
            ++it;
        }
    }
}

size_t ArpTable::size() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return entries_.size();
}

std::vector<std::pair<IpAddress, ArpEntry>> ArpTable::get_all_entries() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<std::pair<IpAddress, ArpEntry>> result;
    for (const auto& pair : entries_) {
        result.push_back(pair);
    }
    return result;
}

void ArpTable::dump() const {
    std::cout << "ARP Table:" << std::endl;
    auto entries = get_all_entries();
    if (entries.empty()) {
        std::cout << "  (empty)" << std::endl;
        return;
    }
    
    for (const auto& pair : entries) {
        std::cout << "  " << pair.first.to_string() << " -> " 
                  << pair.second.to_string() << std::endl;
    }
}

std::string ArpTable::to_string() const {
    std::ostringstream oss;
    auto entries = get_all_entries();
    oss << "ARP Table (" << entries.size() << " entries):" << std::endl;
    for (const auto& pair : entries) {
        oss << "  " << pair.first.to_string() << " -> " 
            << pair.second.to_string() << std::endl;
    }
    return oss.str();
}

// ArpProtocol implementation
ArpProtocol::ArpProtocol(EthernetInterface& interface, const IpAddress& local_ip)
    : interface_(interface), local_ip_(local_ip), cleanup_running_(true) {
    
    // Start cleanup thread
    cleanup_thread_ = std::thread(&ArpProtocol::cleanup_thread_func, this);
}

ArpProtocol::~ArpProtocol() {
    cleanup_running_ = false;
    if (cleanup_thread_.joinable()) {
        cleanup_thread_.join();
    }
}

bool ArpProtocol::send_request(const IpAddress& target_ip) {
    ArpHeader arp_header(ARP_OP_REQUEST, interface_.get_mac_address(), local_ip_,
                        MacAddress(), target_ip);
    
    if (send_arp_packet(arp_header)) {
        stats_.requests_sent++;
        return true;
    }
    return false;
}

bool ArpProtocol::send_reply(const IpAddress& target_ip, const MacAddress& target_mac) {
    ArpHeader arp_header(ARP_OP_REPLY, interface_.get_mac_address(), local_ip_,
                        target_mac, target_ip);
    
    if (send_arp_packet(arp_header)) {
        stats_.replies_sent++;
        return true;
    }
    return false;
}

bool ArpProtocol::send_gratuitous_arp() {
    // Gratuitous ARP: announce our own IP/MAC mapping
    ArpHeader arp_header(ARP_OP_REQUEST, interface_.get_mac_address(), local_ip_,
                        MacAddress("ff:ff:ff:ff:ff:ff"), local_ip_);
    
    if (send_arp_packet(arp_header)) {
        stats_.requests_sent++;
        return true;
    }
    return false;
}

bool ArpProtocol::resolve_address(const IpAddress& ip, MacAddress& mac, 
                                 std::chrono::milliseconds timeout) {
    // First check if we already have it in the table
    if (arp_table_.lookup(ip, mac)) {
        stats_.cache_hits++;
        return true;
    }
    
    stats_.cache_misses++;
    
    // Send ARP request
    if (!send_request(ip)) {
        return false;
    }
    
    // Wait for response with timeout
    auto start_time = std::chrono::steady_clock::now();
    while (std::chrono::steady_clock::now() - start_time < timeout) {
        if (arp_table_.lookup(ip, mac)) {
            return true;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    return false;
}

void ArpProtocol::handle_ethernet_frame(const EthernetFrame& frame) {
    if (frame.header().ethertype != ETHERTYPE_ARP) {
        return;
    }
    
    if (frame.payload_size() < ArpHeader::size()) {
        stats_.invalid_packets++;
        return;
    }
    
    ArpHeader arp_header = ArpHeader::deserialize(frame.payload());
    
    if (!arp_header.is_valid()) {
        stats_.invalid_packets++;
        return;
    }
    
    // Add sender to ARP table
    arp_table_.add_entry(arp_header.sender_ip, arp_header.sender_mac);
    
    if (arp_header.is_request()) {
        handle_arp_request(arp_header);
    } else if (arp_header.is_reply()) {
        handle_arp_reply(arp_header);
    }
}

void ArpProtocol::handle_arp_request(const ArpHeader& arp_header) {
    stats_.requests_received++;
    
    // Check if the request is for our IP
    if (arp_header.target_ip == local_ip_) {
        // Send ARP reply
        send_reply(arp_header.sender_ip, arp_header.sender_mac);
    }
    
    // ARP request handled - no additional user handler needed for basic ARP
}

void ArpProtocol::handle_arp_reply(const ArpHeader& arp_header) {
    stats_.replies_received++;
    
    // Entry is already added to table in handle_ethernet_frame
    // ARP reply handled - no additional user handler needed for basic ARP
}

bool ArpProtocol::send_arp_packet(const ArpHeader& arp_header) {
    // Serialize ARP header
    std::vector<uint8_t> arp_data(ArpHeader::size());
    arp_header.serialize(arp_data.data());
    
    // Determine destination MAC
    MacAddress dst_mac;
    if (arp_header.is_request()) {
        dst_mac = MacAddress("ff:ff:ff:ff:ff:ff"); // Broadcast for requests
    } else {
        dst_mac = arp_header.target_mac; // Unicast for replies
    }
    
    // Create Ethernet frame
    EthernetFrame frame(dst_mac, interface_.get_mac_address(), ETHERTYPE_ARP,
                       arp_data.data(), arp_data.size());
    
    return interface_.send_frame(frame);
}

void ArpProtocol::dump_statistics() const {
    std::cout << "ARP Protocol Statistics:" << std::endl;
    std::cout << "  Requests sent: " << stats_.requests_sent << std::endl;
    std::cout << "  Requests received: " << stats_.requests_received << std::endl;
    std::cout << "  Replies sent: " << stats_.replies_sent << std::endl;
    std::cout << "  Replies received: " << stats_.replies_received << std::endl;
    std::cout << "  Invalid packets: " << stats_.invalid_packets << std::endl;
    std::cout << "  Cache hits: " << stats_.cache_hits << std::endl;
    std::cout << "  Cache misses: " << stats_.cache_misses << std::endl;
}

void ArpProtocol::cleanup_thread_func() {
    while (cleanup_running_) {
        arp_table_.cleanup_expired_entries();

        // Sleep in smaller chunks to be more responsive to shutdown
        for (int i = 0; i < 30 && cleanup_running_; ++i) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
}

// Utility functions
std::string arp_operation_to_string(uint16_t operation) {
    switch (operation) {
        case ARP_OP_REQUEST: return "Request";
        case ARP_OP_REPLY: return "Reply";
        default: return "Unknown";
    }
}

bool is_valid_arp_packet(const uint8_t* data, size_t size) {
    if (size < ArpHeader::size()) {
        return false;
    }
    
    ArpHeader header = ArpHeader::deserialize(data);
    return header.is_valid();
}
