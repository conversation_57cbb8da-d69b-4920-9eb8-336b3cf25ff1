#include "tcp.h"
#include <cstring>
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <thread>
#include <chrono>
#include <random>

// TcpHeader implementation
TcpHeader::TcpHeader()
    : source_port(0), destination_port(0), sequence_number(0), acknowledgment_number(0),
      data_offset_reserved(0), flags(0), window_size(0), checksum(0), urgent_pointer(0) {}

TcpHeader::TcpHeader(uint16_t src_port, uint16_t dst_port, uint32_t seq, uint32_t ack,
                     uint8_t tcp_flags, uint16_t window, uint8_t data_offset)
    : source_port(src_port), destination_port(dst_port),
      sequence_number(seq), acknowledgment_number(ack),
      data_offset_reserved(0), flags(tcp_flags), window_size(window),
      checksum(0), urgent_pointer(0) {
    set_data_offset(data_offset);
}

void TcpHeader::calculate_checksum(const uint8_t* payload, size_t payload_size,
                                  const IpAddress& src_ip, const IpAddress& dst_ip) {
    checksum = 0;
    
    // Create TCP header + payload buffer
    size_t tcp_size = get_header_length() + payload_size;
    std::vector<uint8_t> buffer(tcp_size);
    
    // TCP header
    serialize(buffer.data());
    
    // Payload
    if (payload && payload_size > 0) {
        std::memcpy(buffer.data() + get_header_length(), payload, payload_size);
    }
    
    // Calculate checksum using the utility function
    checksum = calculate_tcp_checksum(buffer.data(), tcp_size, 
                                     src_ip.to_network_order(), dst_ip.to_network_order());
}

bool TcpHeader::verify_checksum(const uint8_t* payload, size_t payload_size,
                                const IpAddress& src_ip, const IpAddress& dst_ip) const {
    uint16_t saved_checksum = checksum;
    const_cast<TcpHeader*>(this)->checksum = 0;
    
    // Create TCP header + payload buffer
    size_t tcp_size = get_header_length() + payload_size;
    std::vector<uint8_t> buffer(tcp_size);
    
    // TCP header
    const_cast<TcpHeader*>(this)->serialize(buffer.data());
    
    // Payload
    if (payload && payload_size > 0) {
        std::memcpy(buffer.data() + get_header_length(), payload, payload_size);
    }
    
    uint16_t calculated = calculate_tcp_checksum(buffer.data(), tcp_size,
                                                src_ip.to_network_order(), dst_ip.to_network_order());
    const_cast<TcpHeader*>(this)->checksum = saved_checksum;
    
    return calculated == saved_checksum;
}

void TcpHeader::serialize(uint8_t* buffer) const {
    *reinterpret_cast<uint16_t*>(buffer) = htons_custom(source_port);
    *reinterpret_cast<uint16_t*>(buffer + 2) = htons_custom(destination_port);
    *reinterpret_cast<uint32_t*>(buffer + 4) = htonl_custom(sequence_number);
    *reinterpret_cast<uint32_t*>(buffer + 8) = htonl_custom(acknowledgment_number);
    buffer[12] = data_offset_reserved;
    buffer[13] = flags;
    *reinterpret_cast<uint16_t*>(buffer + 14) = htons_custom(window_size);
    *reinterpret_cast<uint16_t*>(buffer + 16) = htons_custom(checksum);
    *reinterpret_cast<uint16_t*>(buffer + 18) = htons_custom(urgent_pointer);
}

TcpHeader TcpHeader::deserialize(const uint8_t* buffer) {
    TcpHeader header;
    header.source_port = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer));
    header.destination_port = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 2));
    header.sequence_number = ntohl_custom(*reinterpret_cast<const uint32_t*>(buffer + 4));
    header.acknowledgment_number = ntohl_custom(*reinterpret_cast<const uint32_t*>(buffer + 8));
    header.data_offset_reserved = buffer[12];
    header.flags = buffer[13];
    header.window_size = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 14));
    header.checksum = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 16));
    header.urgent_pointer = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 18));
    return header;
}

bool TcpHeader::is_valid() const {
    uint8_t data_offset = get_data_offset();
    return data_offset >= 5 && data_offset <= 15; // Valid range for data offset
}



std::string TcpHeader::to_string() const {
    std::ostringstream oss;
    oss << "TCP " << source_port << " -> " << destination_port
        << " seq:" << sequence_number << " ack:" << acknowledgment_number
        << " win:" << window_size;

    // Add flags
    std::string flags_str;
    if (has_flag(TCP_FLAG_SYN)) flags_str += "S";
    if (has_flag(TCP_FLAG_ACK)) flags_str += "A";
    if (has_flag(TCP_FLAG_FIN)) flags_str += "F";
    if (has_flag(TCP_FLAG_RST)) flags_str += "R";
    if (has_flag(TCP_FLAG_PSH)) flags_str += "P";
    if (has_flag(TCP_FLAG_URG)) flags_str += "U";

    if (!flags_str.empty()) {
        oss << " [" << flags_str << "]";
    }

    return oss.str();
}

// TcpOptions implementation
TcpOptions::TcpOptions() {}

std::vector<uint8_t> TcpOptions::serialize() const {
    std::vector<uint8_t> options;

    // MSS option
    if (mss != TCP_DEFAULT_MSS) {
        options.push_back(TCP_OPTION_MSS);
        options.push_back(4); // Length
        options.push_back((mss >> 8) & 0xFF);
        options.push_back(mss & 0xFF);
    }

    // Window scale option
    if (window_scale > 0) {
        options.push_back(TCP_OPTION_WINDOW_SCALE);
        options.push_back(3); // Length
        options.push_back(window_scale);
    }

    // SACK permitted option
    if (sack_permitted) {
        options.push_back(TCP_OPTION_SACK_PERMITTED);
        options.push_back(2); // Length
    }

    // Timestamp option
    if (timestamp_enabled) {
        options.push_back(TCP_OPTION_TIMESTAMP);
        options.push_back(10); // Length
        // Timestamp value (4 bytes)
        options.push_back((timestamp_value >> 24) & 0xFF);
        options.push_back((timestamp_value >> 16) & 0xFF);
        options.push_back((timestamp_value >> 8) & 0xFF);
        options.push_back(timestamp_value & 0xFF);
        // Timestamp echo reply (4 bytes)
        options.push_back((timestamp_echo_reply >> 24) & 0xFF);
        options.push_back((timestamp_echo_reply >> 16) & 0xFF);
        options.push_back((timestamp_echo_reply >> 8) & 0xFF);
        options.push_back(timestamp_echo_reply & 0xFF);
    }

    // Pad to 4-byte boundary
    while (options.size() % 4 != 0) {
        options.push_back(TCP_OPTION_NOP);
    }

    return options;
}

bool TcpOptions::deserialize(const uint8_t* data, size_t size) {
    // Reset to defaults
    *this = TcpOptions();

    size_t offset = 0;
    while (offset < size) {
        uint8_t option = data[offset];

        if (option == TCP_OPTION_END) {
            break;
        }

        if (option == TCP_OPTION_NOP) {
            offset++;
            continue;
        }

        if (offset + 1 >= size) {
            return false; // Invalid option length
        }

        uint8_t length = data[offset + 1];
        if (offset + length > size) {
            return false; // Option extends beyond data
        }

        switch (option) {
            case TCP_OPTION_MSS:
                if (length == 4) {
                    mss = (data[offset + 2] << 8) | data[offset + 3];
                }
                break;
            case TCP_OPTION_WINDOW_SCALE:
                if (length == 3) {
                    window_scale = data[offset + 2];
                }
                break;
            case TCP_OPTION_SACK_PERMITTED:
                if (length == 2) {
                    sack_permitted = true;
                }
                break;
            case TCP_OPTION_TIMESTAMP:
                if (length == 10) {
                    timestamp_enabled = true;
                    timestamp_value = (data[offset + 2] << 24) | (data[offset + 3] << 16) |
                                     (data[offset + 4] << 8) | data[offset + 5];
                    timestamp_echo_reply = (data[offset + 6] << 24) | (data[offset + 7] << 16) |
                                          (data[offset + 8] << 8) | data[offset + 9];
                }
                break;
        }

        offset += length;
    }

    return true;
}

std::string TcpOptions::to_string() const {
    std::ostringstream oss;
    oss << "Options: ";
    if (mss != TCP_DEFAULT_MSS) oss << "MSS=" << mss << " ";
    if (window_scale > 0) oss << "WS=" << static_cast<int>(window_scale) << " ";
    if (sack_permitted) oss << "SACK ";
    if (timestamp_enabled) oss << "TS=" << timestamp_value << "/" << timestamp_echo_reply << " ";
    return oss.str();
}

// TcpConnectionKey implementation
TcpConnectionKey::TcpConnectionKey() {}

TcpConnectionKey::TcpConnectionKey(const TcpEndpoint& local_ep, const TcpEndpoint& remote_ep)
    : local(local_ep), remote(remote_ep) {}

bool TcpConnectionKey::operator==(const TcpConnectionKey& other) const {
    return local == other.local && remote == other.remote;
}

bool TcpConnectionKey::operator!=(const TcpConnectionKey& other) const {
    return !(*this == other);
}

bool TcpConnectionKey::operator<(const TcpConnectionKey& other) const {
    if (local < other.local) return true;
    if (other.local < local) return false;
    return remote < other.remote;
}

std::string TcpConnectionKey::to_string() const {
    return local.to_string() + " <-> " + remote.to_string();
}

// TcpSegment implementation
TcpSegment::TcpSegment() {}

TcpSegment::TcpSegment(uint16_t src_port, uint16_t dst_port, uint32_t seq, uint32_t ack,
                       uint8_t tcp_flags, uint16_t window, const uint8_t* payload, size_t payload_size)
    : header_(src_port, dst_port, seq, ack, tcp_flags, window) {
    if (payload && payload_size > 0) {
        set_payload(payload, payload_size);
    }
}

TcpSegment::TcpSegment(const TcpHeader& header, const TcpOptions& options,
                       const uint8_t* payload, size_t payload_size)
    : header_(header), options_(options) {
    if (payload && payload_size > 0) {
        set_payload(payload, payload_size);
    }
}

void TcpSegment::set_payload(const uint8_t* data, size_t size) {
    payload_.resize(size);
    if (data && size > 0) {
        std::memcpy(payload_.data(), data, size);
    }
}

void TcpSegment::set_options(const TcpOptions& opts) {
    options_ = opts;
    // Update header data offset to account for options
    auto options_data = options_.serialize();
    uint8_t data_offset = (TCP_HEADER_MIN_SIZE + options_data.size() + 3) / 4; // Round up to 4-byte boundary
    header_.set_data_offset(data_offset);
}

std::vector<uint8_t> TcpSegment::serialize(const IpAddress& src_ip, const IpAddress& dst_ip) const {
    // Serialize options
    auto options_data = options_.serialize();

    // Calculate total header length
    size_t header_length = TCP_HEADER_MIN_SIZE + options_data.size();
    // Pad to 4-byte boundary
    while (header_length % 4 != 0) {
        header_length++;
    }

    std::vector<uint8_t> segment(header_length + payload_.size());

    // Update header with correct data offset
    TcpHeader header_copy = header_;
    header_copy.set_data_offset(header_length / 4);

    // Calculate checksum
    header_copy.calculate_checksum(payload_.data(), payload_.size(), src_ip, dst_ip);

    // Serialize header
    header_copy.serialize(segment.data());

    // Copy options
    if (!options_data.empty()) {
        std::memcpy(segment.data() + TCP_HEADER_MIN_SIZE, options_data.data(), options_data.size());
    }

    // Pad options to 4-byte boundary
    for (size_t i = TCP_HEADER_MIN_SIZE + options_data.size(); i < header_length; i++) {
        segment[i] = 0; // Padding
    }

    // Copy payload
    if (!payload_.empty()) {
        std::memcpy(segment.data() + header_length, payload_.data(), payload_.size());
    }

    return segment;
}

bool TcpSegment::deserialize(const uint8_t* data, size_t size,
                            const IpAddress& src_ip, const IpAddress& dst_ip) {
    if (size < TCP_HEADER_MIN_SIZE) {
        return false;
    }

    // Deserialize header
    header_ = TcpHeader::deserialize(data);

    if (!header_.is_valid() || header_.get_header_length() > size) {
        return false;
    }

    // Deserialize options if present
    size_t options_size = header_.get_header_length() - TCP_HEADER_MIN_SIZE;
    if (options_size > 0) {
        if (!options_.deserialize(data + TCP_HEADER_MIN_SIZE, options_size)) {
            return false;
        }
    }

    // Extract payload
    size_t payload_size = size - header_.get_header_length();
    if (payload_size > 0) {
        payload_.resize(payload_size);
        std::memcpy(payload_.data(), data + header_.get_header_length(), payload_size);
    } else {
        payload_.clear();
    }

    return true;
}

bool TcpSegment::is_valid(const IpAddress& src_ip, const IpAddress& dst_ip) const {
    return header_.is_valid() && header_.verify_checksum(payload_.data(), payload_.size(), src_ip, dst_ip);
}

std::string TcpSegment::to_string() const {
    std::ostringstream oss;
    oss << header_.to_string() << " (payload: " << payload_.size() << " bytes)";
    return oss.str();
}

void TcpSegment::dump() const {
    std::cout << to_string() << std::endl;
    if (!payload_.empty()) {
        hex_dump(payload_.data(), payload_.size(), "  ");
    }
}

// TcpEndpoint implementation
TcpEndpoint::TcpEndpoint() : port(0) {}

TcpEndpoint::TcpEndpoint(const IpAddress& addr, uint16_t p) : ip(addr), port(p) {}

TcpEndpoint::TcpEndpoint(const std::string& ip_str, uint16_t p) : ip(ip_str), port(p) {}

bool TcpEndpoint::operator==(const TcpEndpoint& other) const {
    return ip == other.ip && port == other.port;
}

bool TcpEndpoint::operator!=(const TcpEndpoint& other) const {
    return !(*this == other);
}

bool TcpEndpoint::operator<(const TcpEndpoint& other) const {
    if (ip < other.ip) return true;
    if (other.ip < ip) return false;
    return port < other.port;
}

std::string TcpEndpoint::to_string() const {
    return ip.to_string() + ":" + std::to_string(port);
}

bool TcpEndpoint::is_valid() const {
    return port > 0; // IP address validation would need to be implemented
}

// Utility functions
std::string tcp_state_to_string(TcpState state) {
    switch (state) {
        case TcpState::CLOSED: return "CLOSED";
        case TcpState::LISTEN: return "LISTEN";
        case TcpState::SYN_SENT: return "SYN_SENT";
        case TcpState::SYN_RECEIVED: return "SYN_RECEIVED";
        case TcpState::ESTABLISHED: return "ESTABLISHED";
        case TcpState::FIN_WAIT_1: return "FIN_WAIT_1";
        case TcpState::FIN_WAIT_2: return "FIN_WAIT_2";
        case TcpState::CLOSE_WAIT: return "CLOSE_WAIT";
        case TcpState::CLOSING: return "CLOSING";
        case TcpState::LAST_ACK: return "LAST_ACK";
        case TcpState::TIME_WAIT: return "TIME_WAIT";
        default: return "UNKNOWN";
    }
}

std::string tcp_flags_to_string(uint8_t flags) {
    std::string result;
    if (flags & TCP_FLAG_URG) result += "URG ";
    if (flags & TCP_FLAG_ACK) result += "ACK ";
    if (flags & TCP_FLAG_PSH) result += "PSH ";
    if (flags & TCP_FLAG_RST) result += "RST ";
    if (flags & TCP_FLAG_SYN) result += "SYN ";
    if (flags & TCP_FLAG_FIN) result += "FIN ";

    if (!result.empty()) {
        result.pop_back(); // Remove trailing space
    }

    return result;
}

bool is_valid_tcp_segment(const uint8_t* data, size_t size) {
    if (size < TCP_HEADER_MIN_SIZE) {
        return false;
    }

    TcpSegment segment;
    IpAddress dummy_src("0.0.0.0"), dummy_dst("0.0.0.0");
    return segment.deserialize(data, size, dummy_src, dummy_dst);
}

std::string tcp_port_to_string(uint16_t port) {
    // Common well-known ports
    switch (port) {
        case 20: return "FTP Data";
        case 21: return "FTP Control";
        case 22: return "SSH";
        case 23: return "Telnet";
        case 25: return "SMTP";
        case 53: return "DNS";
        case 80: return "HTTP";
        case 110: return "POP3";
        case 143: return "IMAP";
        case 443: return "HTTPS";
        case 993: return "IMAPS";
        case 995: return "POP3S";
        default: {
            std::ostringstream oss;
            oss << "Port " << port;
            return oss.str();
        }
    }
}

bool is_valid_tcp_port(uint16_t port) {
    return port >= TCP_MIN_PORT && port <= TCP_MAX_PORT;
}

uint32_t generate_initial_sequence_number() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<uint32_t> dis;
    return dis(gen);
}

bool sequence_less_than(uint32_t a, uint32_t b) {
    return static_cast<int32_t>(a - b) < 0;
}

bool sequence_less_equal(uint32_t a, uint32_t b) {
    return static_cast<int32_t>(a - b) <= 0;
}

uint32_t sequence_add(uint32_t seq, uint32_t offset) {
    return seq + offset;
}

uint32_t sequence_subtract(uint32_t a, uint32_t b) {
    return a - b;
}

// Simple TCP socket implementation to match header interface
// Note: TcpConnection and TcpProtocol are forward declared only
