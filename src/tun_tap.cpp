#include "tun_tap.h"
#include <cstring>
#include <iostream>
#include <sstream>

#ifdef __linux__
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <errno.h>
// Try to include Linux headers, but provide fallbacks if not available
#ifdef __has_include
#if __has_include(<linux/if.h>)
#include <linux/if.h>
#else
#define IFNAMSIZ 16
#endif
#if __has_include(<linux/if_tun.h>)
#include <linux/if_tun.h>
#else
#define IFF_TUN 0x0001
#define IFF_TAP 0x0002
#define IFF_NO_PI 0x1000
#define TUNSETIFF 0x400454ca
#endif
#else
// Fallback definitions for older compilers
#define IFNAMSIZ 16
#define IFF_TUN 0x0001
#define IFF_TAP 0x0002
#define IFF_NO_PI 0x1000
#define TUNSETIFF 0x400454ca
#endif
#else
// Windows placeholder - TUN/TAP not supported
#define IFNAMSIZ 16
#define IFF_TUN 0x0001
#define IFF_TAP 0x0002
#define IFF_NO_PI 0x1000
#define TUNSETIFF 0x400454ca
#endif

// Define ifreq structure if not available
#ifndef _LINUX_IF_H
struct ifreq {
    char ifr_name[IFNAMSIZ];
    int ifr_flags;
};
#endif

// TunTapInterface implementation
TunTapInterface::TunTapInterface(const std::string& device_name, TunTapMode mode, 
                                const MacAddress& mac_addr)
    : EthernetInterface(mac_addr.is_zero() ? generate_random_mac() : mac_addr),
      device_name_(device_name), mode_(mode), fd_(-1), mtu_(1500),
      reception_running_(false) {}

TunTapInterface::~TunTapInterface() {
    close();
}

bool TunTapInterface::open() {
    if (is_open()) {
        Logger::warning("TUN/TAP interface already open");
        return true;
    }

#ifdef __linux__
    // Open TUN/TAP device
    fd_ = ::open("/dev/net/tun", O_RDWR);
    if (fd_ < 0) {
        Logger::error("Failed to open /dev/net/tun: " + std::string(strerror(errno)));
        return false;
    }
#else
    Logger::error("TUN/TAP interfaces not supported on this platform");
    return false;
#endif

#ifdef __linux__
    // Configure interface
    struct ifreq ifr;
    std::memset(&ifr, 0, sizeof(ifr));

    // Set interface name
    if (!device_name_.empty()) {
        strncpy(ifr.ifr_name, device_name_.c_str(), IFNAMSIZ - 1);
    }

    // Set interface type
    if (mode_ == TunTapMode::TUN) {
        ifr.ifr_flags = IFF_TUN | IFF_NO_PI;
    } else {
        ifr.ifr_flags = IFF_TAP | IFF_NO_PI;
    }

    // Create interface
    if (ioctl(fd_, TUNSETIFF, &ifr) < 0) {
        Logger::error("Failed to create TUN/TAP interface: " + std::string(strerror(errno)));
        ::close(fd_);
        fd_ = -1;
        return false;
    }

    // Update device name (kernel may have assigned a different name)
    device_name_ = ifr.ifr_name;

    Logger::info("Created " + std::string(mode_ == TunTapMode::TUN ? "TUN" : "TAP") +
                " interface: " + device_name_);

    return true;
#endif
}

void TunTapInterface::close() {
    if (!is_open()) return;

    stop_reception();

#ifdef __linux__
    ::close(fd_);
#endif
    fd_ = -1;

    Logger::info("Closed TUN/TAP interface: " + device_name_);
}

bool TunTapInterface::set_ip_address(const IpAddress& ip, int prefix_length) {
    return TunTapUtils::set_device_ip(device_name_, ip, prefix_length);
}

bool TunTapInterface::set_mtu(int mtu) {
    if (TunTapUtils::set_device_mtu(device_name_, mtu)) {
        mtu_ = mtu;
        return true;
    }
    return false;
}

bool TunTapInterface::bring_up() {
    return TunTapUtils::bring_device_up(device_name_);
}

bool TunTapInterface::bring_down() {
    return TunTapUtils::bring_device_down(device_name_);
}

bool TunTapInterface::send_frame(const EthernetFrame& frame) {
    if (!is_open()) {
        Logger::error("TUN/TAP interface not open");
        return false;
    }
    
    if (mode_ == TunTapMode::TUN) {
        Logger::error("Cannot send Ethernet frame on TUN interface");
        return false;
    }
    
    auto data = frame.serialize();
    return send_packet(data.data(), data.size());
}

bool TunTapInterface::send_packet(const uint8_t* data, size_t size) {
    if (!is_open()) {
        Logger::error("TUN/TAP interface not open");
        return false;
    }

#ifdef __linux__
    ssize_t written = write(fd_, data, size);
    if (written < 0) {
        Logger::error("Failed to write to TUN/TAP interface: " + std::string(strerror(errno)));
        return false;
    }

    if (static_cast<size_t>(written) != size) {
        Logger::warning("Partial write to TUN/TAP interface");
        return false;
    }

    stats_.frames_sent++;
    stats_.bytes_sent += size;

    return true;
#else
    Logger::error("TUN/TAP send not supported on this platform");
    return false;
#endif
}

void TunTapInterface::start_reception() {
    if (!is_open()) {
        Logger::error("TUN/TAP interface not open");
        return;
    }
    
    if (reception_running_) {
        Logger::warning("Reception already running");
        return;
    }
    
    reception_running_ = true;
    reception_thread_ = std::thread(&TunTapInterface::reception_thread, this);
    
    Logger::info("Started reception thread for " + device_name_);
}

void TunTapInterface::stop_reception() {
    if (!reception_running_) return;
    
    reception_running_ = false;
    
    if (reception_thread_.joinable()) {
        reception_thread_.join();
    }
    
    Logger::info("Stopped reception thread for " + device_name_);
}

void TunTapInterface::dump_statistics() const {
    std::cout << "TUN/TAP Interface Statistics for " << device_name_ << ":" << std::endl;
    std::cout << "  Frames sent: " << stats_.frames_sent << std::endl;
    std::cout << "  Frames received: " << stats_.frames_received << std::endl;
    std::cout << "  Bytes sent: " << stats_.bytes_sent << std::endl;
    std::cout << "  Bytes received: " << stats_.bytes_received << std::endl;
    std::cout << "  Frames dropped: " << stats_.frames_dropped << std::endl;
    std::cout << "  CRC errors: " << stats_.crc_errors << std::endl;
}

void TunTapInterface::reception_thread() {
    Logger::debug("Reception thread started for " + device_name_);

    while (reception_running_) {
#ifdef __linux__
        ssize_t bytes_read = read(fd_, reception_buffer_, BUFFER_SIZE);

        if (bytes_read < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                continue;
            }
            Logger::error("Failed to read from TUN/TAP interface: " + std::string(strerror(errno)));
            break;
        }

        if (bytes_read == 0) {
            Logger::warning("EOF on TUN/TAP interface");
            break;
        }

        handle_received_data(reception_buffer_, bytes_read);
#else
        // On non-Linux platforms, just sleep to avoid busy waiting
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
#endif
    }

    Logger::debug("Reception thread stopped for " + device_name_);
}

void TunTapInterface::handle_received_data(const uint8_t* data, size_t size) {
    if (mode_ == TunTapMode::TAP) {
        // TAP mode: data is an Ethernet frame
        EthernetFrame frame;
        if (frame.deserialize(data, size)) {
            handle_received_frame(frame);
        } else {
            Logger::warning("Failed to parse Ethernet frame");
            stats_.crc_errors++;
        }
    } else {
        // TUN mode: data is an IP packet
        // For TUN mode, we need to create a fake Ethernet frame
        if (size >= 20) { // Minimum IP header size
            uint8_t version = (data[0] >> 4) & 0x0F;
            if (version == 4) {
                // Create fake Ethernet frame for IPv4 packet
                EthernetFrame frame(mac_address_, MacAddress(), ETHERTYPE_IPV4, data, size);
                handle_received_frame(frame);
            } else {
                Logger::warning("Unsupported IP version: " + std::to_string(version));
            }
        } else {
            Logger::warning("Received packet too small for IP header");
        }
    }
}

// TunTapUtils implementation
bool TunTapUtils::create_device(const std::string& device_name, TunTapMode mode) {
    std::string command = "ip tuntap add dev " + device_name + " mode " + 
                         (mode == TunTapMode::TUN ? "tun" : "tap");
    return execute_command(command);
}

bool TunTapUtils::delete_device(const std::string& device_name) {
    std::string command = "ip tuntap del dev " + device_name;
    return execute_command(command);
}

bool TunTapUtils::set_device_ip(const std::string& device_name, 
                               const IpAddress& ip, int prefix_length) {
    std::string command = "ip addr add " + ip.to_string() + "/" + 
                         std::to_string(prefix_length) + " dev " + device_name;
    return execute_command(command);
}

bool TunTapUtils::set_device_mtu(const std::string& device_name, int mtu) {
    std::string command = "ip link set dev " + device_name + " mtu " + std::to_string(mtu);
    return execute_command(command);
}

bool TunTapUtils::bring_device_up(const std::string& device_name) {
    std::string command = "ip link set dev " + device_name + " up";
    return execute_command(command);
}

bool TunTapUtils::bring_device_down(const std::string& device_name) {
    std::string command = "ip link set dev " + device_name + " down";
    return execute_command(command);
}

bool TunTapUtils::device_exists(const std::string& device_name) {
    std::string command = "ip link show " + device_name + " > /dev/null 2>&1";
    return execute_command(command);
}

bool TunTapUtils::get_device_info(const std::string& device_name, 
                                 IpAddress& ip, int& prefix_length, int& mtu) {
    // This is a simplified implementation
    // In a real implementation, you would parse the output of ip commands
    return false;
}

std::vector<std::string> TunTapUtils::list_devices() {
    // This is a simplified implementation
    // In a real implementation, you would parse the output of ip commands
    return {};
}

bool TunTapUtils::execute_command(const std::string& command) {
    int result = system(command.c_str());
    return result == 0;
}

bool TunTapUtils::parse_ip_output(const std::string& output, 
                                 IpAddress& ip, int& prefix_length) {
    // This is a simplified implementation
    // In a real implementation, you would parse the ip command output
    return false;
}

// TunTapManager implementation
TunTapManager& TunTapManager::instance() {
    static TunTapManager instance;
    return instance;
}

TunTapManager::~TunTapManager() {
    cleanup_all();
}

std::shared_ptr<TunTapInterface> TunTapManager::create_interface(const std::string& name, 
                                                               TunTapMode mode,
                                                               const MacAddress& mac) {
    std::lock_guard<std::mutex> lock(interfaces_mutex_);
    
    auto interface = std::make_shared<TunTapInterface>(name, mode, mac);
    if (interface->open()) {
        interfaces_[name] = interface;
        return interface;
    }
    
    return nullptr;
}

bool TunTapManager::remove_interface(const std::string& name) {
    std::lock_guard<std::mutex> lock(interfaces_mutex_);
    
    auto it = interfaces_.find(name);
    if (it != interfaces_.end()) {
        it->second->close();
        interfaces_.erase(it);
        return true;
    }
    
    return false;
}

std::shared_ptr<TunTapInterface> TunTapManager::get_interface(const std::string& name) {
    std::lock_guard<std::mutex> lock(interfaces_mutex_);
    
    auto it = interfaces_.find(name);
    if (it != interfaces_.end()) {
        return it->second;
    }
    
    return nullptr;
}

void TunTapManager::cleanup_all() {
    std::lock_guard<std::mutex> lock(interfaces_mutex_);
    
    for (auto& pair : interfaces_) {
        pair.second->close();
    }
    
    interfaces_.clear();
}

std::vector<std::string> TunTapManager::list_interfaces() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(interfaces_mutex_));
    
    std::vector<std::string> names;
    for (const auto& pair : interfaces_) {
        names.push_back(pair.first);
    }
    
    return names;
}
