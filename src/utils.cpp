#include "utils.h"
#include <arpa/inet.h>
#include <cstring>
#include <sstream>
#include <iomanip>
#include <random>
#include <chrono>
#include <algorithm>

// Network byte order conversion utilities
uint16_t htons_custom(uint16_t hostshort) {
    return htons(hostshort);
}

uint32_t htonl_custom(uint32_t hostlong) {
    return htonl(hostlong);
}

uint16_t ntohs_custom(uint16_t netshort) {
    return ntohs(netshort);
}

uint32_t ntohl_custom(uint32_t netlong) {
    return ntohl(netlong);
}

// Checksum calculation utilities
uint16_t calculate_checksum(const void* data, size_t length) {
    const uint16_t* ptr = static_cast<const uint16_t*>(data);
    uint32_t sum = 0;
    
    // Sum all 16-bit words
    while (length > 1) {
        sum += *ptr++;
        length -= 2;
    }
    
    // Add the last byte if present
    if (length == 1) {
        sum += *(static_cast<const uint8_t*>(data) + (ptr - static_cast<const uint16_t*>(data)) * 2);
    }
    
    // Add carry bits
    while (sum >> 16) {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }
    
    return ~sum;
}

uint16_t calculate_ip_checksum(const void* header, size_t length) {
    return calculate_checksum(header, length);
}

uint16_t calculate_tcp_checksum(const void* tcp_header, size_t tcp_length, 
                               uint32_t src_ip, uint32_t dst_ip) {
    // Create pseudo header
    struct {
        uint32_t src_ip;
        uint32_t dst_ip;
        uint8_t zero;
        uint8_t protocol;
        uint16_t tcp_length;
    } pseudo_header;
    
    pseudo_header.src_ip = src_ip;
    pseudo_header.dst_ip = dst_ip;
    pseudo_header.zero = 0;
    pseudo_header.protocol = 6; // TCP
    pseudo_header.tcp_length = htons_custom(tcp_length);
    
    // Calculate checksum over pseudo header + TCP header + data
    uint32_t sum = 0;
    
    // Add pseudo header
    const uint16_t* ptr = reinterpret_cast<const uint16_t*>(&pseudo_header);
    for (size_t i = 0; i < sizeof(pseudo_header) / 2; ++i) {
        sum += ptr[i];
    }
    
    // Add TCP header and data
    ptr = static_cast<const uint16_t*>(tcp_header);
    size_t remaining = tcp_length;
    while (remaining > 1) {
        sum += *ptr++;
        remaining -= 2;
    }
    
    // Add last byte if present
    if (remaining == 1) {
        sum += *(reinterpret_cast<const uint8_t*>(ptr));
    }
    
    // Add carry bits
    while (sum >> 16) {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }
    
    return ~sum;
}

uint16_t calculate_udp_checksum(const void* udp_header, size_t udp_length,
                               uint32_t src_ip, uint32_t dst_ip) {
    // Create pseudo header
    struct {
        uint32_t src_ip;
        uint32_t dst_ip;
        uint8_t zero;
        uint8_t protocol;
        uint16_t udp_length;
    } pseudo_header;
    
    pseudo_header.src_ip = src_ip;
    pseudo_header.dst_ip = dst_ip;
    pseudo_header.zero = 0;
    pseudo_header.protocol = 17; // UDP
    pseudo_header.udp_length = htons_custom(udp_length);
    
    // Calculate checksum over pseudo header + UDP header + data
    uint32_t sum = 0;
    
    // Add pseudo header
    const uint16_t* ptr = reinterpret_cast<const uint16_t*>(&pseudo_header);
    for (size_t i = 0; i < sizeof(pseudo_header) / 2; ++i) {
        sum += ptr[i];
    }
    
    // Add UDP header and data
    ptr = static_cast<const uint16_t*>(udp_header);
    size_t remaining = udp_length;
    while (remaining > 1) {
        sum += *ptr++;
        remaining -= 2;
    }
    
    // Add last byte if present
    if (remaining == 1) {
        sum += *(reinterpret_cast<const uint8_t*>(ptr));
    }
    
    // Add carry bits
    while (sum >> 16) {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }
    
    return ~sum;
}

// MacAddress implementation
MacAddress::MacAddress() {
    std::memset(addr, 0, 6);
}

MacAddress::MacAddress(const uint8_t* mac) {
    std::memcpy(addr, mac, 6);
}

MacAddress::MacAddress(const std::string& mac_str) {
    std::memset(addr, 0, 6);
    
    std::istringstream iss(mac_str);
    std::string token;
    int i = 0;
    
    while (std::getline(iss, token, ':') && i < 6) {
        addr[i++] = static_cast<uint8_t>(std::stoul(token, nullptr, 16));
    }
}

bool MacAddress::operator==(const MacAddress& other) const {
    return std::memcmp(addr, other.addr, 6) == 0;
}

bool MacAddress::operator!=(const MacAddress& other) const {
    return !(*this == other);
}

std::string MacAddress::to_string() const {
    std::ostringstream oss;
    oss << std::hex << std::setfill('0');
    for (int i = 0; i < 6; ++i) {
        if (i > 0) oss << ":";
        oss << std::setw(2) << static_cast<int>(addr[i]);
    }
    return oss.str();
}

bool MacAddress::is_broadcast() const {
    for (int i = 0; i < 6; ++i) {
        if (addr[i] != 0xFF) return false;
    }
    return true;
}

bool MacAddress::is_zero() const {
    for (int i = 0; i < 6; ++i) {
        if (addr[i] != 0) return false;
    }
    return true;
}

// IpAddress implementation
IpAddress::IpAddress() : addr(0) {}

IpAddress::IpAddress(uint32_t ip) : addr(ip) {}

IpAddress::IpAddress(const std::string& ip_str) {
    if (inet_pton(AF_INET, ip_str.c_str(), &addr) != 1) {
        addr = 0; // Invalid IP address
    }
}

IpAddress::IpAddress(uint8_t a, uint8_t b, uint8_t c, uint8_t d) {
    // inet_pton stores in network byte order, so we need to match that
    uint32_t host_addr = (static_cast<uint32_t>(a) << 24) |
                        (static_cast<uint32_t>(b) << 16) |
                        (static_cast<uint32_t>(c) << 8) |
                        static_cast<uint32_t>(d);
    addr = htonl_custom(host_addr);
}

bool IpAddress::operator==(const IpAddress& other) const {
    return addr == other.addr;
}

bool IpAddress::operator!=(const IpAddress& other) const {
    return addr != other.addr;
}

bool IpAddress::operator<(const IpAddress& other) const {
    return ntohl_custom(addr) < ntohl_custom(other.addr);
}

std::string IpAddress::to_string() const {
    char buffer[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &addr, buffer, INET_ADDRSTRLEN);
    return std::string(buffer);
}

uint32_t IpAddress::to_network_order() const {
    return htonl_custom(addr);
}

IpAddress IpAddress::from_network_order(uint32_t net_addr) {
    return IpAddress(ntohl_custom(net_addr));
}

// Logger implementation
LogLevel Logger::current_level = LogLevel::INFO;

void Logger::set_level(LogLevel level) {
    current_level = level;
}

void Logger::log(LogLevel level, const std::string& message) {
    if (level >= current_level) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;
        
        std::cout << "[" << std::put_time(std::localtime(&time_t), "%H:%M:%S")
                  << "." << std::setfill('0') << std::setw(3) << ms.count()
                  << "] [" << level_to_string(level) << "] " << message << std::endl;
    }
}

void Logger::debug(const std::string& message) {
    log(LogLevel::DEBUG, message);
}

void Logger::info(const std::string& message) {
    log(LogLevel::INFO, message);
}

void Logger::warning(const std::string& message) {
    log(LogLevel::WARNING, message);
}

void Logger::error(const std::string& message) {
    log(LogLevel::ERROR, message);
}

std::string Logger::level_to_string(LogLevel level) {
    switch (level) {
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARN";
        case LogLevel::ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

// Hex dump utility
void hex_dump(const void* data, size_t length, const std::string& prefix) {
    const uint8_t* bytes = static_cast<const uint8_t*>(data);
    
    for (size_t i = 0; i < length; i += 16) {
        std::cout << prefix << std::hex << std::setfill('0') << std::setw(4) << i << ": ";
        
        // Print hex bytes
        for (size_t j = 0; j < 16; ++j) {
            if (i + j < length) {
                std::cout << std::setw(2) << static_cast<int>(bytes[i + j]) << " ";
            } else {
                std::cout << "   ";
            }
            if (j == 7) std::cout << " ";
        }
        
        std::cout << " |";
        
        // Print ASCII representation
        for (size_t j = 0; j < 16 && i + j < length; ++j) {
            char c = bytes[i + j];
            std::cout << (std::isprint(c) ? c : '.');
        }
        
        std::cout << "|" << std::endl;
    }
    std::cout << std::dec;
}

// Time utilities
uint64_t get_current_time_ms() {
    auto now = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
}

uint64_t get_current_time_us() {
    auto now = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch()).count();
}

// Buffer implementation
Buffer::Buffer() {}

Buffer::Buffer(size_t initial_size) : buffer_(initial_size) {}

Buffer::Buffer(const void* data, size_t size) {
    buffer_.resize(size);
    std::memcpy(buffer_.data(), data, size);
}

void Buffer::append(const void* data, size_t size) {
    size_t old_size = buffer_.size();
    buffer_.resize(old_size + size);
    std::memcpy(buffer_.data() + old_size, data, size);
}

void Buffer::prepend(const void* data, size_t size) {
    buffer_.insert(buffer_.begin(), static_cast<const uint8_t*>(data), 
                   static_cast<const uint8_t*>(data) + size);
}

void Buffer::consume(size_t bytes) {
    if (bytes >= buffer_.size()) {
        buffer_.clear();
    } else {
        buffer_.erase(buffer_.begin(), buffer_.begin() + bytes);
    }
}

void Buffer::clear() {
    buffer_.clear();
}

void Buffer::resize(size_t new_size) {
    buffer_.resize(new_size);
}

// Random number utilities
uint32_t random_uint32() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<uint32_t> dis;
    return dis(gen);
}

uint16_t random_uint16() {
    return static_cast<uint16_t>(random_uint32() & 0xFFFF);
}

// String utilities
std::vector<std::string> split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::istringstream iss(str);
    std::string token;
    
    while (std::getline(iss, token, delimiter)) {
        tokens.push_back(token);
    }
    
    return tokens;
}

std::string trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r");
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\n\r");
    return str.substr(start, end - start + 1);
}
