#include "icmp.h"
#include "ethernet.h"
#include <cstring>
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <thread>
#include <chrono>

// IcmpHeader implementation
IcmpHeader::IcmpHeader() : type(0), code(0), checksum(0), rest(0) {}

IcmpHeader::IcmpHeader(uint8_t msg_type, uint8_t msg_code, uint32_t rest_data)
    : type(msg_type), code(msg_code), checksum(0), rest(rest_data) {}

void IcmpHeader::set_echo_data(uint16_t identifier, uint16_t sequence) {
    rest = (static_cast<uint32_t>(identifier) << 16) | sequence;
}

uint16_t IcmpHeader::get_identifier() const {
    return (rest >> 16) & 0xFFFF;
}

uint16_t IcmpHeader::get_sequence() const {
    return rest & 0xFFFF;
}

void IcmpHeader::calculate_checksum(const uint8_t* payload, size_t payload_size) {
    checksum = 0;

    // Create temporary buffer with header + payload
    size_t total_size = size() + payload_size;
    std::vector<uint8_t> buffer(total_size);

    // Copy header
    serialize(buffer.data());

    // Copy payload
    if (payload && payload_size > 0) {
        std::memcpy(buffer.data() + size(), payload, payload_size);
    }

    // Calculate checksum over entire ICMP message
    checksum = calculate_ip_checksum(buffer.data(), total_size);
}

bool IcmpHeader::verify_checksum(const uint8_t* payload, size_t payload_size) const {
    uint16_t saved_checksum = checksum;
    const_cast<IcmpHeader*>(this)->checksum = 0;

    // Create temporary buffer
    size_t total_size = size() + payload_size;
    std::vector<uint8_t> buffer(total_size);

    // Copy header
    const_cast<IcmpHeader*>(this)->serialize(buffer.data());

    // Copy payload
    if (payload && payload_size > 0) {
        std::memcpy(buffer.data() + size(), payload, payload_size);
    }

    uint16_t calculated = calculate_ip_checksum(buffer.data(), total_size);
    const_cast<IcmpHeader*>(this)->checksum = saved_checksum;

    return calculated == saved_checksum;
}

void IcmpHeader::serialize(uint8_t* buffer) const {
    buffer[0] = type;
    buffer[1] = code;
    *reinterpret_cast<uint16_t*>(buffer + 2) = htons_custom(checksum);
    *reinterpret_cast<uint32_t*>(buffer + 4) = htonl_custom(rest);
}

IcmpHeader IcmpHeader::deserialize(const uint8_t* buffer) {
    IcmpHeader header;
    header.type = buffer[0];
    header.code = buffer[1];
    header.checksum = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 2));
    header.rest = ntohl_custom(*reinterpret_cast<const uint32_t*>(buffer + 4));
    return header;
}

std::string IcmpHeader::to_string() const {
    std::ostringstream oss;
    oss << "ICMP " << icmp_type_to_string(type);
    if (type == ICMP_TYPE_ECHO_REQUEST || type == ICMP_TYPE_ECHO_REPLY) {
        uint16_t identifier = (rest >> 16) & 0xFFFF;
        uint16_t sequence = rest & 0xFFFF;
        oss << " (id: " << identifier << ", seq: " << sequence << ")";
    }
    return oss.str();
}

// IcmpMessage implementation
IcmpMessage::IcmpMessage() {}

IcmpMessage::IcmpMessage(uint8_t type, uint8_t code, const uint8_t* payload, size_t payload_size)
    : header_(type, code, 0) {
    if (payload && payload_size > 0) {
        set_payload(payload, payload_size);
    }
}

IcmpMessage::IcmpMessage(const IcmpHeader& header, const uint8_t* payload, size_t payload_size)
    : header_(header) {
    if (payload && payload_size > 0) {
        set_payload(payload, payload_size);
    }
}

// Static factory methods
IcmpMessage IcmpMessage::create_echo_request(uint16_t identifier, uint16_t sequence,
                                           const uint8_t* payload, size_t payload_size) {
    IcmpHeader header(ICMP_TYPE_ECHO_REQUEST, 0);
    header.set_echo_data(identifier, sequence);
    return IcmpMessage(header, payload, payload_size);
}

IcmpMessage IcmpMessage::create_echo_reply(uint16_t identifier, uint16_t sequence,
                                         const uint8_t* payload, size_t payload_size) {
    IcmpHeader header(ICMP_TYPE_ECHO_REPLY, 0);
    header.set_echo_data(identifier, sequence);
    return IcmpMessage(header, payload, payload_size);
}

IcmpMessage IcmpMessage::create_dest_unreachable(uint8_t code, const uint8_t* original_packet, size_t packet_size) {
    IcmpHeader header(ICMP_TYPE_DEST_UNREACHABLE, code);
    return IcmpMessage(header, original_packet, packet_size);
}

void IcmpMessage::set_payload(const uint8_t* data, size_t size) {
    payload_.resize(size);
    if (data && size > 0) {
        std::memcpy(payload_.data(), data, size);
    }
}

std::vector<uint8_t> IcmpMessage::serialize() const {
    std::vector<uint8_t> message(IcmpHeader::size() + payload_.size());

    // Calculate checksum
    IcmpHeader header_copy = header_;
    header_copy.calculate_checksum(payload_.data(), payload_.size());

    // Serialize header
    header_copy.serialize(message.data());

    // Copy payload
    if (!payload_.empty()) {
        std::memcpy(message.data() + IcmpHeader::size(), payload_.data(), payload_.size());
    }

    return message;
}

bool IcmpMessage::deserialize(const uint8_t* data, size_t size) {
    if (size < IcmpHeader::size()) {
        return false;
    }

    // Deserialize header
    header_ = IcmpHeader::deserialize(data);

    // Extract payload
    size_t payload_size = size - IcmpHeader::size();
    if (payload_size > 0) {
        payload_.resize(payload_size);
        std::memcpy(payload_.data(), data + IcmpHeader::size(), payload_size);
    } else {
        payload_.clear();
    }

    return true;
}

bool IcmpMessage::is_valid() const {
    return header_.verify_checksum(payload_.data(), payload_.size());
}

std::string IcmpMessage::to_string() const {
    std::ostringstream oss;
    oss << header_.to_string() << " (payload: " << payload_.size() << " bytes)";
    return oss.str();
}

void IcmpMessage::dump() const {
    std::cout << to_string() << std::endl;
    if (!payload_.empty()) {
        hex_dump(payload_.data(), payload_.size(), "  ");
    }
}

// IcmpEchoHeader implementation
IcmpEchoHeader::IcmpEchoHeader() : type(0), code(0), checksum(0), identifier(0), sequence(0) {}

IcmpEchoHeader::IcmpEchoHeader(uint8_t msg_type, uint16_t id, uint16_t seq)
    : type(msg_type), code(0), checksum(0), identifier(id), sequence(seq) {}

void IcmpEchoHeader::calculate_checksum(const uint8_t* payload, size_t payload_size) {
    checksum = 0;

    // Create temporary buffer with header + payload
    size_t total_size = size() + payload_size;
    std::vector<uint8_t> buffer(total_size);

    // Copy header
    serialize(buffer.data());

    // Copy payload
    if (payload && payload_size > 0) {
        std::memcpy(buffer.data() + size(), payload, payload_size);
    }

    // Calculate checksum over entire ICMP message
    checksum = calculate_ip_checksum(buffer.data(), total_size);
}

bool IcmpEchoHeader::verify_checksum(const uint8_t* payload, size_t payload_size) const {
    uint16_t saved_checksum = checksum;
    const_cast<IcmpEchoHeader*>(this)->checksum = 0;

    // Create temporary buffer
    size_t total_size = size() + payload_size;
    std::vector<uint8_t> buffer(total_size);

    // Copy header
    const_cast<IcmpEchoHeader*>(this)->serialize(buffer.data());

    // Copy payload
    if (payload && payload_size > 0) {
        std::memcpy(buffer.data() + size(), payload, payload_size);
    }

    uint16_t calculated = calculate_ip_checksum(buffer.data(), total_size);
    const_cast<IcmpEchoHeader*>(this)->checksum = saved_checksum;

    return calculated == saved_checksum;
}

void IcmpEchoHeader::serialize(uint8_t* buffer) const {
    buffer[0] = type;
    buffer[1] = code;
    *reinterpret_cast<uint16_t*>(buffer + 2) = htons_custom(checksum);
    *reinterpret_cast<uint16_t*>(buffer + 4) = htons_custom(identifier);
    *reinterpret_cast<uint16_t*>(buffer + 6) = htons_custom(sequence);
}

IcmpEchoHeader IcmpEchoHeader::deserialize(const uint8_t* buffer) {
    IcmpEchoHeader header;
    header.type = buffer[0];
    header.code = buffer[1];
    header.checksum = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 2));
    header.identifier = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 4));
    header.sequence = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 6));
    return header;
}

std::string IcmpEchoHeader::to_string() const {
    std::ostringstream oss;
    oss << "ICMP " << icmp_type_to_string(type) << " (id: " << identifier << ", seq: " << sequence << ")";
    return oss.str();
}

// IcmpEchoMessage implementation
IcmpEchoMessage::IcmpEchoMessage() {}

IcmpEchoMessage::IcmpEchoMessage(uint8_t type, uint16_t identifier, uint16_t sequence,
                                const uint8_t* payload, size_t payload_size)
    : header_(type, identifier, sequence) {
    if (payload && payload_size > 0) {
        set_payload(payload, payload_size);
    }
}

void IcmpEchoMessage::set_payload(const uint8_t* data, size_t size) {
    payload_.resize(size);
    if (data && size > 0) {
        std::memcpy(payload_.data(), data, size);
    }
}

std::vector<uint8_t> IcmpEchoMessage::serialize() const {
    std::vector<uint8_t> message(IcmpEchoHeader::size() + payload_.size());

    // Calculate checksum
    IcmpEchoHeader header_copy = header_;
    header_copy.calculate_checksum(payload_.data(), payload_.size());

    // Serialize header
    header_copy.serialize(message.data());

    // Copy payload
    if (!payload_.empty()) {
        std::memcpy(message.data() + IcmpEchoHeader::size(), payload_.data(), payload_.size());
    }

    return message;
}

bool IcmpEchoMessage::deserialize(const uint8_t* data, size_t size) {
    if (size < IcmpEchoHeader::size()) {
        return false;
    }

    // Deserialize header
    header_ = IcmpEchoHeader::deserialize(data);

    // Extract payload
    size_t payload_size = size - IcmpEchoHeader::size();
    if (payload_size > 0) {
        payload_.resize(payload_size);
        std::memcpy(payload_.data(), data + IcmpEchoHeader::size(), payload_size);
    } else {
        payload_.clear();
    }

    return true;
}

bool IcmpEchoMessage::is_valid() const {
    return header_.verify_checksum(payload_.data(), payload_.size());
}

IcmpEchoMessage IcmpEchoMessage::create_reply() const {
    uint8_t reply_type = (header_.type == ICMP_TYPE_ECHO_REQUEST) ? ICMP_TYPE_ECHO_REPLY : ICMP_TYPE_ECHO_REQUEST;
    return IcmpEchoMessage(reply_type, header_.identifier, header_.sequence, payload_.data(), payload_.size());
}



std::string IcmpEchoMessage::to_string() const {
    std::ostringstream oss;
    oss << header_.to_string() << " (payload: " << payload_.size() << " bytes)";
    return oss.str();
}

void IcmpEchoMessage::dump() const {
    std::cout << to_string() << std::endl;
    if (!payload_.empty()) {
        hex_dump(payload_.data(), payload_.size(), "  ");
    }
}

// Utility functions
std::string icmp_type_to_string(uint8_t type) {
    switch (type) {
        case ICMP_TYPE_ECHO_REPLY: return "Echo Reply";
        case ICMP_TYPE_DEST_UNREACHABLE: return "Destination Unreachable";
        case ICMP_TYPE_SOURCE_QUENCH: return "Source Quench";
        case ICMP_TYPE_REDIRECT: return "Redirect";
        case ICMP_TYPE_ECHO_REQUEST: return "Echo Request";
        case ICMP_TYPE_TIME_EXCEEDED: return "Time Exceeded";
        case ICMP_TYPE_PARAM_PROBLEM: return "Parameter Problem";
        case ICMP_TYPE_TIMESTAMP_REQUEST: return "Timestamp Request";
        case ICMP_TYPE_TIMESTAMP_REPLY: return "Timestamp Reply";
        default: {
            std::ostringstream oss;
            oss << "Type " << static_cast<int>(type);
            return oss.str();
        }
    }
}

std::string icmp_code_to_string(uint8_t type, uint8_t code) {
    if (type == ICMP_TYPE_DEST_UNREACHABLE) {
        switch (code) {
            case ICMP_CODE_NET_UNREACHABLE: return "Network Unreachable";
            case ICMP_CODE_HOST_UNREACHABLE: return "Host Unreachable";
            case ICMP_CODE_PROTOCOL_UNREACHABLE: return "Protocol Unreachable";
            case ICMP_CODE_PORT_UNREACHABLE: return "Port Unreachable";
            case ICMP_CODE_FRAGMENTATION_NEEDED: return "Fragmentation Needed";
            case ICMP_CODE_SOURCE_ROUTE_FAILED: return "Source Route Failed";
            default: break;
        }
    } else if (type == ICMP_TYPE_TIME_EXCEEDED) {
        switch (code) {
            case ICMP_CODE_TTL_EXCEEDED: return "TTL Exceeded";
            case ICMP_CODE_FRAGMENT_REASSEMBLY_TIMEOUT: return "Fragment Reassembly Timeout";
            default: break;
        }
    }
    
    std::ostringstream oss;
    oss << "Code " << static_cast<int>(code);
    return oss.str();
}

bool is_valid_icmp_message(const uint8_t* data, size_t size) {
    if (size < IcmpHeader::size()) {
        return false;
    }

    IcmpMessage msg;
    return msg.deserialize(data, size) && msg.is_valid();
}

// IcmpProtocol implementation
IcmpProtocol::IcmpProtocol(EthernetInterface& interface, const IpAddress& local_ip)
    : ipv4_(nullptr), interface_(&interface), local_ip_(local_ip), auto_reply_echo_(true) {}

IcmpProtocol::IcmpProtocol(Ipv4Protocol& ipv4)
    : ipv4_(&ipv4), interface_(nullptr), auto_reply_echo_(true) {}

IcmpProtocol::~IcmpProtocol() {}

bool IcmpProtocol::send_echo_request(const IpAddress& target, uint16_t identifier, uint16_t sequence,
                                   const uint8_t* payload, size_t payload_size) {
    IcmpMessage msg = IcmpMessage::create_echo_request(identifier, sequence, payload, payload_size);
    stats_.echo_requests_sent++;
    return send_icmp_message(target, msg);
}

bool IcmpProtocol::send_echo_reply(const IpAddress& target, uint16_t identifier, uint16_t sequence,
                                 const uint8_t* payload, size_t payload_size) {
    IcmpMessage msg = IcmpMessage::create_echo_reply(identifier, sequence, payload, payload_size);
    stats_.echo_replies_sent++;
    return send_icmp_message(target, msg);
}

bool IcmpProtocol::send_icmp_message(const IpAddress& target, const IcmpMessage& message) {
    // For testing mode, we'll simulate sending by creating a frame
    if (interface_) {
        // This is a simplified implementation for testing
        return true;
    }

    // For production mode with IPv4 protocol
    if (ipv4_) {
        auto serialized = message.serialize();
        // Would send via IPv4 protocol here
        return true;
    }

    return false;
}

void IcmpProtocol::dump_statistics() const {
    std::cout << "ICMP Statistics:" << std::endl;
    std::cout << "  Echo requests sent: " << stats_.echo_requests_sent << std::endl;
    std::cout << "  Echo replies sent: " << stats_.echo_replies_sent << std::endl;
    std::cout << "  Echo requests received: " << stats_.echo_requests_received << std::endl;
    std::cout << "  Echo replies received: " << stats_.echo_replies_received << std::endl;
}
