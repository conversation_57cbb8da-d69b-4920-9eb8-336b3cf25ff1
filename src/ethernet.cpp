#include "ethernet.h"
#include <cstring>
#include <iostream>
#include <iomanip>

// EthernetHeader implementation
EthernetHeader::EthernetHeader() : ethertype(0) {}

EthernetHeader::EthernetHeader(const MacAddress& dst, const MacAddress& src, uint16_t type)
    : dst_mac(dst), src_mac(src), ethertype(type) {}

void EthernetHeader::serialize(uint8_t* buffer) const {
    std::memcpy(buffer, dst_mac.addr, 6);
    std::memcpy(buffer + 6, src_mac.addr, 6);
    *reinterpret_cast<uint16_t*>(buffer + 12) = htons_custom(ethertype);
}

EthernetHeader EthernetHeader::deserialize(const uint8_t* buffer) {
    EthernetHeader header;
    std::memcpy(header.dst_mac.addr, buffer, 6);
    std::memcpy(header.src_mac.addr, buffer + 6, 6);
    header.ethertype = ntohs_custom(*reinterpret_cast<const uint16_t*>(buffer + 12));
    return header;
}

std::string EthernetHeader::to_string() const {
    std::ostringstream oss;
    oss << "Ethernet: " << src_mac.to_string() << " -> " << dst_mac.to_string()
        << " (type: 0x" << std::hex << ethertype << ")";
    return oss.str();
}

// EthernetFrame implementation
EthernetFrame::EthernetFrame() {}

EthernetFrame::EthernetFrame(const MacAddress& dst_mac, const MacAddress& src_mac, 
                            uint16_t ethertype, const uint8_t* payload, size_t payload_size)
    : header_(dst_mac, src_mac, ethertype) {
    if (payload && payload_size > 0) {
        set_payload(payload, payload_size);
    }
}

void EthernetFrame::set_payload(const uint8_t* data, size_t size) {
    payload_.resize(size);
    if (data && size > 0) {
        std::memcpy(payload_.data(), data, size);
    }
}

std::vector<uint8_t> EthernetFrame::serialize() const {
    std::vector<uint8_t> frame(ETHERNET_HEADER_SIZE + payload_.size());
    
    // Serialize header
    header_.serialize(frame.data());
    
    // Copy payload
    if (!payload_.empty()) {
        std::memcpy(frame.data() + ETHERNET_HEADER_SIZE, payload_.data(), payload_.size());
    }
    
    return frame;
}

bool EthernetFrame::deserialize(const uint8_t* data, size_t size) {
    if (size < ETHERNET_HEADER_SIZE) {
        return false;
    }
    
    // Deserialize header
    header_ = EthernetHeader::deserialize(data);
    
    // Extract payload
    size_t payload_size = size - ETHERNET_HEADER_SIZE;
    if (payload_size > 0) {
        payload_.resize(payload_size);
        std::memcpy(payload_.data(), data + ETHERNET_HEADER_SIZE, payload_size);
    } else {
        payload_.clear();
    }
    
    return true;
}

bool EthernetFrame::is_valid() const {
    // Check frame size (excluding CRC)
    size_t frame_size = ETHERNET_HEADER_SIZE + payload_.size();
    // Allow smaller frames for testing purposes, just check maximum
    return frame_size <= (ETHERNET_MAX_FRAME_SIZE - 4);
}

bool EthernetFrame::is_broadcast() const {
    return header_.dst_mac.is_broadcast();
}

bool EthernetFrame::is_multicast() const {
    return (header_.dst_mac.addr[0] & 0x01) != 0 && !is_broadcast();
}

std::string EthernetFrame::to_string() const {
    std::ostringstream oss;
    oss << header_.to_string() << " (payload: " << payload_.size() << " bytes)";
    return oss.str();
}

void EthernetFrame::dump() const {
    std::cout << to_string() << std::endl;
    if (!payload_.empty()) {
        hex_dump(payload_.data(), payload_.size(), "  ");
    }
}

// EthernetInterface implementation
EthernetInterface::EthernetInterface(const MacAddress& mac_addr) 
    : mac_address_(mac_addr) {}

void EthernetInterface::handle_received_frame(const EthernetFrame& frame) {
    stats_.frames_received++;
    stats_.bytes_received += frame.total_size();
    
    // Check if frame is for us (unicast to our MAC, broadcast, or multicast)
    if (frame.header().dst_mac == mac_address_ || 
        frame.is_broadcast() || 
        frame.is_multicast()) {
        
        if (frame_handler_) {
            frame_handler_(frame);
        }
    } else {
        stats_.frames_dropped++;
    }
}

// Utility functions
bool is_valid_mac_address(const std::string& mac_str) {
    if (mac_str.length() != 17) return false;
    
    for (size_t i = 0; i < mac_str.length(); ++i) {
        if (i % 3 == 2) {
            if (mac_str[i] != ':') return false;
        } else {
            if (!std::isxdigit(mac_str[i])) return false;
        }
    }
    
    return true;
}

MacAddress generate_random_mac() {
    MacAddress mac;
    for (int i = 0; i < 6; ++i) {
        mac.addr[i] = static_cast<uint8_t>(random_uint32() & 0xFF);
    }
    
    // Set locally administered bit and clear multicast bit
    mac.addr[0] = (mac.addr[0] & 0xFE) | 0x02;
    
    return mac;
}

std::string ethertype_to_string(uint16_t ethertype) {
    switch (ethertype) {
        case ETHERTYPE_IPV4: return "IPv4";
        case ETHERTYPE_ARP: return "ARP";
        case ETHERTYPE_IPV6: return "IPv6";
        default: {
            std::ostringstream oss;
            oss << "0x" << std::hex << ethertype;
            return oss.str();
        }
    }
}
