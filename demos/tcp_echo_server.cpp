#include "tcp.h"
#include "ipv4.h"
#include "ethernet.h"
#include <iostream>
#include <string>

class TcpEchoServerDemo {
public:
    TcpEchoServerDemo(uint16_t port = 7) : port_(port) {}

    void demonstrate() {
        std::cout << "=== TCP Echo Server Demo ===" << std::endl;
        std::cout << "This demo shows how to handle TCP connections and echo data" << std::endl;
        std::cout << "Server port: " << port_ << std::endl;
        std::cout << std::endl;

        // Simulate a complete TCP connection
        simulate_tcp_connection();
    }
    
private:
    void simulate_tcp_connection() {
        std::cout << "1. Simulating TCP 3-way handshake..." << std::endl;

        uint16_t client_port = 12345;
        uint32_t client_isn = generate_initial_sequence_number();
        uint32_t server_isn = generate_initial_sequence_number();

        // Step 1: Client SYN
        TcpSegment syn(client_port, port_, client_isn, 0, TCP_FLAG_SYN, TCP_MAX_WINDOW_SIZE);
        std::cout << "   Client SYN: " << syn.to_string() << std::endl;

        // Step 2: Server SYN-ACK
        TcpSegment syn_ack(port_, client_port, server_isn, client_isn + 1,
                          TCP_FLAG_SYN | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "   Server SYN-ACK: " << syn_ack.to_string() << std::endl;

        // Step 3: Client ACK
        TcpSegment ack(client_port, port_, client_isn + 1, server_isn + 1,
                      TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "   Client ACK: " << ack.to_string() << std::endl;
        std::cout << "   Connection established!" << std::endl;

        // Simulate data exchange
        simulate_data_exchange(client_port, client_isn + 1, server_isn + 1);
    }

    void simulate_data_exchange(uint16_t client_port, uint32_t client_seq, uint32_t server_seq) {
        std::cout << "\n2. Simulating TCP data exchange..." << std::endl;

        // Client sends data
        std::string message = "Hello, TCP server!";
        TcpSegment data(client_port, port_, client_seq, server_seq,
                       TCP_FLAG_PSH | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE,
                       reinterpret_cast<const uint8_t*>(message.c_str()), message.length());
        std::cout << "   Client data: " << data.to_string() << std::endl;
        std::cout << "   Message: \"" << message << "\"" << std::endl;

        // Server ACK
        TcpSegment data_ack(port_, client_port, server_seq, client_seq + message.length(),
                           TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "   Server ACK: " << data_ack.to_string() << std::endl;

        // Server echo reply
        TcpSegment echo_reply(port_, client_port, server_seq, client_seq + message.length(),
                             TCP_FLAG_PSH | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE,
                             reinterpret_cast<const uint8_t*>(message.c_str()), message.length());
        std::cout << "   Server echo: " << echo_reply.to_string() << std::endl;
        std::cout << "   Echoed: \"" << message << "\"" << std::endl;

        // Client ACK for echo
        TcpSegment echo_ack(client_port, port_, client_seq + message.length(),
                           server_seq + message.length(), TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "   Client ACK: " << echo_ack.to_string() << std::endl;

        // Simulate connection teardown
        simulate_connection_teardown(client_port, client_seq + message.length(),
                                   server_seq + message.length());
    }

    void simulate_connection_teardown(uint16_t client_port, uint32_t client_seq, uint32_t server_seq) {
        std::cout << "\n3. Simulating TCP connection teardown..." << std::endl;

        // Client FIN
        TcpSegment client_fin(client_port, port_, client_seq, server_seq,
                             TCP_FLAG_FIN | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "   Client FIN: " << client_fin.to_string() << std::endl;

        // Server ACK
        TcpSegment fin_ack(port_, client_port, server_seq, client_seq + 1,
                          TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "   Server ACK: " << fin_ack.to_string() << std::endl;

        // Server FIN
        TcpSegment server_fin(port_, client_port, server_seq, client_seq + 1,
                             TCP_FLAG_FIN | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "   Server FIN: " << server_fin.to_string() << std::endl;

        // Client ACK
        TcpSegment final_ack(client_port, port_, client_seq + 1, server_seq + 1,
                            TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "   Client ACK: " << final_ack.to_string() << std::endl;
        std::cout << "   Connection closed!" << std::endl;

        // Show packet structure
        show_tcp_packet_structure();
    }
    void show_tcp_packet_structure() {
        std::cout << "\n4. TCP packet structure demonstration:" << std::endl;

        // Create sample IP addresses
        IpAddress client_ip("***********00");
        IpAddress server_ip("***********");

        // Create sample MAC addresses
        MacAddress client_mac("aa:bb:cc:dd:ee:ff");
        MacAddress server_mac("02:00:00:00:00:01");

        std::cout << "\n   Ethernet Frame:" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Dst MAC: " << server_mac.to_string() << "     │" << std::endl;
        std::cout << "   │ Src MAC: " << client_mac.to_string() << "     │" << std::endl;
        std::cout << "   │ EtherType: 0x0800 (IPv4)               │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   IPv4 Header:" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Version: 4, Header Length: 20 bytes    │" << std::endl;
        std::cout << "   │ Protocol: 6 (TCP)                      │" << std::endl;
        std::cout << "   │ Source IP: " << client_ip.to_string() << "           │" << std::endl;
        std::cout << "   │ Dest IP: " << server_ip.to_string() << "             │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   TCP Header:" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Source Port: 12345                     │" << std::endl;
        std::cout << "   │ Dest Port: 7 (echo)                    │" << std::endl;
        std::cout << "   │ Sequence Number: (varies)               │" << std::endl;
        std::cout << "   │ Acknowledgment Number: (varies)         │" << std::endl;
        std::cout << "   │ Flags: SYN, ACK, PSH, FIN (as needed)  │" << std::endl;
        std::cout << "   │ Window Size: 65535                     │" << std::endl;
        std::cout << "   │ Checksum: (calculated)                  │" << std::endl;
        std::cout << "   │ Data: \"Hello, TCP server!\" (if any)     │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   TCP connection demonstrates:" << std::endl;
        std::cout << "   • Reliable connection establishment (3-way handshake)" << std::endl;
        std::cout << "   • Ordered data delivery with acknowledgments" << std::endl;
        std::cout << "   • Flow control with window size management" << std::endl;
        std::cout << "   • Graceful connection termination (4-way handshake)" << std::endl;
        std::cout << "   • Error detection with checksums" << std::endl;
    }
private:
    uint16_t port_;
};

int main(int argc, char* argv[]) {
    uint16_t port = 7; // Standard echo port

    // Parse command line arguments
    if (argc > 1) {
        port = static_cast<uint16_t>(std::stoi(argv[1]));
    }

    TcpEchoServerDemo demo(port);
    demo.demonstrate();

    return 0;
}

