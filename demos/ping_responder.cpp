#include "icmp.h"
#include "ipv4.h"
#include "ethernet.h"
#include <iostream>
#include <signal.h>
#include <thread>
#include <chrono>

class PingResponderDemo {
public:
    PingResponderDemo() = default;

    void demonstrate() {
        std::cout << "=== ICMP Ping Responder Demo ===" << std::endl;
        std::cout << "This demo shows how to handle ICMP echo requests" << std::endl;
        std::cout << std::endl;

        // Simulate receiving a ping request
        simulate_ping_request();
    }

private:
    void simulate_ping_request() {
        std::cout << "1. Simulating incoming ICMP echo request..." << std::endl;

        // Create a sample ping request
        std::string ping_data = "Hello, ping!";
        IcmpMessage ping_request(ICMP_TYPE_ECHO_REQUEST, 0,
                                reinterpret_cast<const uint8_t*>(ping_data.c_str()),
                                ping_data.length());

        // Set identifier and sequence number
        uint16_t identifier = 1234;
        uint16_t sequence = 1;
        uint32_t rest_of_header = (static_cast<uint32_t>(identifier) << 16) | sequence;

        std::cout << "   Received: " << ping_request.to_string() << std::endl;
        std::cout << "   Ping data: \"" << ping_data << "\"" << std::endl;

        // Create ping reply
        create_ping_reply(ping_request, identifier, sequence);
    }
    
    void create_ping_reply(const IcmpMessage& ping_request, uint16_t identifier, uint16_t sequence) {
        std::cout << "\n2. Creating ICMP echo reply..." << std::endl;

        // Create ICMP echo reply with same payload
        IcmpMessage ping_reply(ICMP_TYPE_ECHO_REPLY, 0,
                              ping_request.payload(), ping_request.payload_size());

        std::cout << "   Reply: " << ping_reply.to_string() << std::endl;

        // Show the complete packet structure
        show_packet_structure(ping_request, ping_reply);
    }

    void show_packet_structure(const IcmpMessage& request, const IcmpMessage& reply) {
        std::cout << "\n3. Complete packet structure demonstration:" << std::endl;

        // Create sample IP addresses
        IpAddress client_ip("***********00");
        IpAddress server_ip("***********");

        // Create sample MAC addresses
        MacAddress client_mac("aa:bb:cc:dd:ee:ff");
        MacAddress server_mac("02:00:00:00:00:01");

        std::cout << "\n   Ethernet Frame (Request):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Dst MAC: " << server_mac.to_string() << "     │" << std::endl;
        std::cout << "   │ Src MAC: " << client_mac.to_string() << "     │" << std::endl;
        std::cout << "   │ EtherType: 0x0800 (IPv4)               │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   IPv4 Header (Request):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Version: 4, Header Length: 20 bytes    │" << std::endl;
        std::cout << "   │ Protocol: 1 (ICMP)                     │" << std::endl;
        std::cout << "   │ Source IP: " << client_ip.to_string() << "           │" << std::endl;
        std::cout << "   │ Dest IP: " << server_ip.to_string() << "             │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   ICMP Header (Request):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Type: 8 (Echo Request)                  │" << std::endl;
        std::cout << "   │ Code: 0                                 │" << std::endl;
        std::cout << "   │ Checksum: (calculated)                  │" << std::endl;
        std::cout << "   │ Identifier: 1234, Sequence: 1          │" << std::endl;
        std::cout << "   │ Data: \"Hello, ping!\"                   │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   ICMP Header (Reply):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Type: 0 (Echo Reply)                    │" << std::endl;
        std::cout << "   │ Code: 0                                 │" << std::endl;
        std::cout << "   │ Checksum: (calculated)                  │" << std::endl;
        std::cout << "   │ Identifier: 1234, Sequence: 1          │" << std::endl;
        std::cout << "   │ Data: \"Hello, ping!\" (echoed back)      │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   Processing complete! Ping reply would be sent back to client." << std::endl;
    }
};

int main(int argc, char* argv[]) {
    (void)argc; // Suppress unused parameter warning
    (void)argv; // Suppress unused parameter warning

    PingResponderDemo demo;
    demo.demonstrate();

    return 0;
}
