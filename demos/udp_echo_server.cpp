#include "udp.h"
#include "ipv4.h"
#include "ethernet.h"
#include <iostream>
#include <string>

class UdpEchoServerDemo {
public:
    UdpEchoServerDemo(uint16_t port = 7) : port_(port) {}

    void demonstrate() {
        std::cout << "=== UDP Echo Server Demo ===" << std::endl;
        std::cout << "This demo shows how to handle UDP echo requests" << std::endl;
        std::cout << "Server port: " << port_ << std::endl;
        std::cout << std::endl;

        // Simulate receiving a UDP packet
        simulate_udp_echo();
    }
    
private:
    void simulate_udp_echo() {
        std::cout << "1. Simulating incoming UDP packet..." << std::endl;

        // Create sample UDP data
        std::string message = "Hello, UDP server!";
        uint16_t client_port = 12345;

        // Create UDP datagram
        UdpDatagram request(client_port, port_,
                           reinterpret_cast<const uint8_t*>(message.c_str()),
                           message.length());

        std::cout << "   Received: " << request.to_string() << std::endl;
        std::cout << "   Message: \"" << message << "\"" << std::endl;

        // Create echo reply
        create_udp_echo(request, message);
    }

    void create_udp_echo(const UdpDatagram& request, const std::string& message) {
        std::cout << "\n2. Creating UDP echo reply..." << std::endl;

        // Create UDP echo reply (swap source and destination ports)
        UdpDatagram reply(port_, request.header().source_port,
                         reinterpret_cast<const uint8_t*>(message.c_str()),
                         message.length());

        std::cout << "   Reply: " << reply.to_string() << std::endl;

        // Show the complete packet structure
        show_udp_packet_structure(request, reply);
    }

    void show_udp_packet_structure(const UdpDatagram& request, const UdpDatagram& reply) {
        std::cout << "\n3. Complete UDP packet structure demonstration:" << std::endl;

        // Create sample IP addresses
        IpAddress client_ip("***********00");
        IpAddress server_ip("***********");

        // Create sample MAC addresses
        MacAddress client_mac("aa:bb:cc:dd:ee:ff");
        MacAddress server_mac("02:00:00:00:00:01");

        std::cout << "\n   Ethernet Frame (Request):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Dst MAC: " << server_mac.to_string() << "     │" << std::endl;
        std::cout << "   │ Src MAC: " << client_mac.to_string() << "     │" << std::endl;
        std::cout << "   │ EtherType: 0x0800 (IPv4)               │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   IPv4 Header (Request):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Version: 4, Header Length: 20 bytes    │" << std::endl;
        std::cout << "   │ Protocol: 17 (UDP)                     │" << std::endl;
        std::cout << "   │ Source IP: " << client_ip.to_string() << "           │" << std::endl;
        std::cout << "   │ Dest IP: " << server_ip.to_string() << "             │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   UDP Header (Request):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Source Port: " << request.header().source_port << "                    │" << std::endl;
        std::cout << "   │ Dest Port: " << request.header().destination_port << "                        │" << std::endl;
        std::cout << "   │ Length: " << request.header().length << " bytes                     │" << std::endl;
        std::cout << "   │ Checksum: (calculated)                  │" << std::endl;
        std::cout << "   │ Data: \"Hello, UDP server!\"              │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   UDP Header (Reply):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Source Port: " << reply.header().source_port << "                        │" << std::endl;
        std::cout << "   │ Dest Port: " << reply.header().destination_port << "                    │" << std::endl;
        std::cout << "   │ Length: " << reply.header().length << " bytes                     │" << std::endl;
        std::cout << "   │ Checksum: (calculated)                  │" << std::endl;
        std::cout << "   │ Data: \"Hello, UDP server!\" (echoed)     │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   Processing complete! UDP echo reply would be sent back to client." << std::endl;
    }
private:
    uint16_t port_;
};

int main(int argc, char* argv[]) {
    uint16_t port = 7; // Standard echo port

    // Parse command line arguments
    if (argc > 1) {
        port = static_cast<uint16_t>(std::stoi(argv[1]));
    }

    UdpEchoServerDemo demo(port);
    demo.demonstrate();

    return 0;
}
