#include "tcp.h"
#include "ipv4.h"
#include "ethernet.h"
#include <iostream>
#include <string>
#include <sstream>

class SimpleHttpClientDemo {
public:
    SimpleHttpClientDemo() = default;

    void demonstrate(const std::string& host, const std::string& path = "/", uint16_t port = 80) {
        std::cout << "=== Simple HTTP Client Demo ===" << std::endl;
        std::cout << "This demo simulates an HTTP GET request using our TCP/IP stack" << std::endl;
        std::cout << "Target: " << host << ":" << port << path << std::endl;
        std::cout << std::endl;

        // Create HTTP GET request
        std::ostringstream http_request;
        http_request << "GET " << path << " HTTP/1.1\r\n";
        http_request << "Host: " << host << "\r\n";
        http_request << "User-Agent: SimpleTcpStack/1.0\r\n";
        http_request << "Connection: close\r\n";
        http_request << "\r\n";

        std::string request_str = http_request.str();
        std::cout << "HTTP Request:\n" << request_str << std::endl;

        // Simulate TCP connection and data transfer
        simulate_tcp_connection(host, port, request_str);
    }
    
private:
    void simulate_tcp_connection(const std::string& host, uint16_t port, const std::string& data) {
        // For demo purposes, we'll simulate the TCP connection process
        // In a real implementation, this would involve actual network communication

        IpAddress server_ip(host); // Assume host is an IP address for simplicity
        IpAddress client_ip("********");
        uint16_t client_port = 12345;

        std::cout << "\n=== TCP Connection Simulation ===" << std::endl;

        // Step 1: TCP 3-way handshake
        uint32_t client_isn = generate_initial_sequence_number();
        uint32_t server_isn = generate_initial_sequence_number();

        // SYN
        TcpSegment syn(client_port, port, client_isn, 0, TCP_FLAG_SYN, TCP_MAX_WINDOW_SIZE);
        std::cout << "1. Client SYN: " << syn.to_string() << std::endl;

        // SYN-ACK (simulated server response)
        TcpSegment syn_ack(port, client_port, server_isn, client_isn + 1,
                          TCP_FLAG_SYN | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "2. Server SYN-ACK: " << syn_ack.to_string() << std::endl;

        // ACK
        TcpSegment ack(client_port, port, client_isn + 1, server_isn + 1,
                      TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "3. Client ACK: " << ack.to_string() << std::endl;
        std::cout << "   Connection established!" << std::endl;

        // Step 2: Send HTTP request
        std::cout << "\n=== HTTP Data Transfer ===" << std::endl;
        TcpSegment http_data(client_port, port, client_isn + 1, server_isn + 1,
                            TCP_FLAG_PSH | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE,
                            reinterpret_cast<const uint8_t*>(data.c_str()), data.length());
        std::cout << "4. HTTP Request: " << http_data.to_string() << std::endl;

        // Step 3: Simulate HTTP response
        std::string http_response =
            "HTTP/1.1 200 OK\r\n"
            "Content-Type: text/html\r\n"
            "Content-Length: 13\r\n"
            "Connection: close\r\n"
            "\r\n"
            "Hello, World!";

        TcpSegment response_data(port, client_port, server_isn + 1, client_isn + 1 + data.length(),
                                TCP_FLAG_PSH | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE,
                                reinterpret_cast<const uint8_t*>(http_response.c_str()), http_response.length());
        std::cout << "5. HTTP Response: " << response_data.to_string() << std::endl;
        std::cout << "   Response data:\n" << http_response << std::endl;

        // Step 4: Connection teardown
        std::cout << "\n=== TCP Connection Teardown ===" << std::endl;

        // Server FIN (connection close)
        TcpSegment server_fin(port, client_port, server_isn + 1 + http_response.length(),
                             client_isn + 1 + data.length(),
                             TCP_FLAG_FIN | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "6. Server FIN: " << server_fin.to_string() << std::endl;

        // Client ACK
        TcpSegment client_ack(client_port, port, client_isn + 1 + data.length(),
                             server_isn + 2 + http_response.length(),
                             TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "7. Client ACK: " << client_ack.to_string() << std::endl;

        // Client FIN
        TcpSegment client_fin(client_port, port, client_isn + 1 + data.length(),
                             server_isn + 2 + http_response.length(),
                             TCP_FLAG_FIN | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "8. Client FIN: " << client_fin.to_string() << std::endl;

        // Server ACK
        TcpSegment final_ack(port, client_port, server_isn + 2 + http_response.length(),
                            client_isn + 2 + data.length(),
                            TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
        std::cout << "9. Server ACK: " << final_ack.to_string() << std::endl;
        std::cout << "   Connection closed!" << std::endl;

        show_http_packet_structure();
    }
    void show_http_packet_structure() {
        std::cout << "\n=== HTTP over TCP/IP Packet Structure ===" << std::endl;

        // Create sample IP addresses
        IpAddress client_ip("*************");
        IpAddress server_ip("*************"); // example.com

        // Create sample MAC addresses
        MacAddress client_mac("aa:bb:cc:dd:ee:ff");
        MacAddress server_mac("02:00:00:00:00:01");

        std::cout << "\n   Application Layer (HTTP):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ GET / HTTP/1.1                         │" << std::endl;
        std::cout << "   │ Host: example.com                      │" << std::endl;
        std::cout << "   │ User-Agent: SimpleTcpStack/1.0          │" << std::endl;
        std::cout << "   │ Connection: close                       │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   Transport Layer (TCP):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Source Port: 12345                     │" << std::endl;
        std::cout << "   │ Dest Port: 80 (HTTP)                   │" << std::endl;
        std::cout << "   │ Sequence Number: (varies)               │" << std::endl;
        std::cout << "   │ Acknowledgment Number: (varies)         │" << std::endl;
        std::cout << "   │ Flags: PSH, ACK                        │" << std::endl;
        std::cout << "   │ Window Size: 65535                     │" << std::endl;
        std::cout << "   │ Checksum: (calculated)                  │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   Network Layer (IPv4):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Version: 4, Header Length: 20 bytes    │" << std::endl;
        std::cout << "   │ Protocol: 6 (TCP)                      │" << std::endl;
        std::cout << "   │ Source IP: " << client_ip.to_string() << "           │" << std::endl;
        std::cout << "   │ Dest IP: " << server_ip.to_string() << "      │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   Data Link Layer (Ethernet):" << std::endl;
        std::cout << "   ┌─────────────────────────────────────────┐" << std::endl;
        std::cout << "   │ Dst MAC: " << server_mac.to_string() << "     │" << std::endl;
        std::cout << "   │ Src MAC: " << client_mac.to_string() << "     │" << std::endl;
        std::cout << "   │ EtherType: 0x0800 (IPv4)               │" << std::endl;
        std::cout << "   └─────────────────────────────────────────┘" << std::endl;

        std::cout << "\n   This demonstrates the complete TCP/IP stack:" << std::endl;
        std::cout << "   • Application layer: HTTP protocol" << std::endl;
        std::cout << "   • Transport layer: TCP with reliable delivery" << std::endl;
        std::cout << "   • Network layer: IPv4 with routing" << std::endl;
        std::cout << "   • Data link layer: Ethernet framing" << std::endl;
    }
};

int main(int argc, char* argv[]) {
    std::string host = "*************"; // example.com IP
    std::string path = "/";
    uint16_t port = 80;

    // Parse command line arguments
    if (argc > 1) {
        host = argv[1];
    }
    if (argc > 2) {
        path = argv[2];
    }
    if (argc > 3) {
        port = static_cast<uint16_t>(std::stoi(argv[3]));
    }

    SimpleHttpClientDemo demo;
    demo.demonstrate(host, path, port);

    std::cout << "\nHTTP GET simulation completed successfully!" << std::endl;
    return 0;
}
