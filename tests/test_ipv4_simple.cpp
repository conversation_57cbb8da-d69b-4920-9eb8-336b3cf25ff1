#include "ipv4.h"
#include <iostream>
#include <cassert>
#include <cstring>

void test_ipv4_header_basic() {
    std::cout << "Testing IPv4 header basic..." << std::endl;
    
    // Test default constructor
    Ipv4Header header1;
    std::cout << "  Version: " << static_cast<int>(header1.get_version()) << std::endl;
    std::cout << "  IHL: " << static_cast<int>(header1.get_ihl()) << std::endl;
    std::cout << "  Header length: " << static_cast<int>(header1.get_header_length()) << std::endl;
    
    assert(header1.get_version() == 4);
    assert(header1.get_ihl() == 5);
    assert(header1.get_header_length() == 20);
    
    // Test parameterized constructor
    IpAddress src("***********");
    IpAddress dst("***********");
    Ipv4Header header2(src, dst, IPV4_PROTOCOL_TCP, 100, 64);
    
    std::cout << "  Source: " << header2.source_ip.to_string() << std::endl;
    std::cout << "  Destination: " << header2.destination_ip.to_string() << std::endl;
    std::cout << "  Protocol: " << static_cast<int>(header2.protocol) << std::endl;
    std::cout << "  Total length: " << header2.total_length << std::endl;
    
    assert(header2.source_ip == src);
    assert(header2.destination_ip == dst);
    assert(header2.protocol == IPV4_PROTOCOL_TCP);
    assert(header2.total_length == 120); // 20 + 100
    
    std::cout << "IPv4 header basic tests passed!" << std::endl;
}

void test_ipv4_serialization() {
    std::cout << "Testing IPv4 serialization..." << std::endl;
    
    IpAddress src("********");
    IpAddress dst("********");
    Ipv4Header header(src, dst, IPV4_PROTOCOL_UDP, 50);
    
    // Calculate checksum before serialization
    header.calculate_checksum();
    std::cout << "  Calculated checksum: 0x" << std::hex << header.header_checksum << std::dec << std::endl;
    
    // Test serialization
    std::vector<uint8_t> buffer(20);
    header.serialize(buffer.data());
    
    // Test deserialization
    Ipv4Header header2 = Ipv4Header::deserialize(buffer.data());
    
    std::cout << "  Original: " << header.to_string() << std::endl;
    std::cout << "  Deserialized: " << header2.to_string() << std::endl;
    
    assert(header2.source_ip == src);
    assert(header2.destination_ip == dst);
    assert(header2.protocol == IPV4_PROTOCOL_UDP);
    assert(header2.total_length == 70); // 20 + 50
    assert(header2.header_checksum == header.header_checksum);
    
    std::cout << "IPv4 serialization tests passed!" << std::endl;
}

void test_ipv4_checksum() {
    std::cout << "Testing IPv4 checksum..." << std::endl;
    
    IpAddress src("***********");
    IpAddress dst("***********");
    Ipv4Header header(src, dst, IPV4_PROTOCOL_TCP, 100);
    
    // Calculate checksum
    header.calculate_checksum();
    std::cout << "  Checksum: 0x" << std::hex << header.header_checksum << std::dec << std::endl;
    
    // Verify checksum
    bool valid = header.verify_checksum();
    std::cout << "  Checksum valid: " << (valid ? "yes" : "no") << std::endl;
    assert(valid);
    
    // Test header validity
    bool header_valid = header.is_valid();
    std::cout << "  Header valid: " << (header_valid ? "yes" : "no") << std::endl;
    assert(header_valid);
    
    std::cout << "IPv4 checksum tests passed!" << std::endl;
}

void test_ipv4_packet_simple() {
    std::cout << "Testing IPv4 packet simple..." << std::endl;
    
    IpAddress src("********");
    IpAddress dst("********");
    
    // Create packet without payload first
    Ipv4Packet packet1(src, dst, IPV4_PROTOCOL_UDP, nullptr, 0);
    
    std::cout << "  Packet: " << packet1.to_string() << std::endl;
    std::cout << "  Header valid: " << (packet1.header().is_valid() ? "yes" : "no") << std::endl;
    
    // Test serialization
    auto serialized = packet1.serialize();
    std::cout << "  Serialized size: " << serialized.size() << std::endl;
    
    // Test deserialization
    Ipv4Packet packet2;
    bool deserialize_ok = packet2.deserialize(serialized.data(), serialized.size());
    std::cout << "  Deserialization: " << (deserialize_ok ? "success" : "failed") << std::endl;
    assert(deserialize_ok);
    
    std::cout << "  Deserialized: " << packet2.to_string() << std::endl;
    
    // Check if packet is valid
    bool packet_valid = packet2.is_valid();
    std::cout << "  Packet valid: " << (packet_valid ? "yes" : "no") << std::endl;
    
    if (!packet_valid) {
        std::cout << "  Header checksum: 0x" << std::hex << packet2.header().header_checksum << std::dec << std::endl;
        std::cout << "  Checksum verification: " << (packet2.header().verify_checksum() ? "pass" : "fail") << std::endl;
    }
    
    assert(packet_valid);
    
    std::cout << "IPv4 packet simple tests passed!" << std::endl;
}

void test_routing_table_basic() {
    std::cout << "Testing routing table basic..." << std::endl;
    
    RoutingTable table;
    assert(table.size() == 0);
    
    // Add a simple route
    RouteEntry route(IpAddress("***********"), IpAddress("*************"),
                     IpAddress("***********"), "eth0", 10);
    table.add_route(route);
    assert(table.size() == 1);
    
    // Test lookup
    RouteEntry found_route;
    bool found = table.lookup_route(IpAddress("***********00"), found_route);
    std::cout << "  Route lookup: " << (found ? "found" : "not found") << std::endl;
    assert(found);
    
    std::cout << "  Route: " << found_route.to_string() << std::endl;
    
    std::cout << "Routing table basic tests passed!" << std::endl;
}

void test_ipv4_utilities_basic() {
    std::cout << "Testing IPv4 utilities basic..." << std::endl;
    
    // Test protocol strings
    std::cout << "  ICMP: " << ipv4_protocol_to_string(IPV4_PROTOCOL_ICMP) << std::endl;
    std::cout << "  TCP: " << ipv4_protocol_to_string(IPV4_PROTOCOL_TCP) << std::endl;
    std::cout << "  UDP: " << ipv4_protocol_to_string(IPV4_PROTOCOL_UDP) << std::endl;
    
    assert(ipv4_protocol_to_string(IPV4_PROTOCOL_ICMP) == "ICMP");
    assert(ipv4_protocol_to_string(IPV4_PROTOCOL_TCP) == "TCP");
    assert(ipv4_protocol_to_string(IPV4_PROTOCOL_UDP) == "UDP");
    
    // Test network calculations
    IpAddress ip("***********00");
    IpAddress netmask("*************");
    
    IpAddress network = calculate_network_address(ip, netmask);
    IpAddress broadcast = calculate_broadcast_address(network, netmask);
    
    std::cout << "  IP: " << ip.to_string() << std::endl;
    std::cout << "  Netmask: " << netmask.to_string() << std::endl;
    std::cout << "  Network: " << network.to_string() << std::endl;
    std::cout << "  Broadcast: " << broadcast.to_string() << std::endl;
    
    assert(network == IpAddress("***********"));
    assert(broadcast == IpAddress("*************"));
    
    std::cout << "IPv4 utilities basic tests passed!" << std::endl;
}

int main() {
    std::cout << "=== Simple IPv4 Protocol Tests ===" << std::endl;
    std::cout << "Testing IPv4 implementation step by step" << std::endl;
    std::cout << std::endl;
    
    try {
        test_ipv4_header_basic();
        test_ipv4_serialization();
        test_ipv4_checksum();
        test_ipv4_packet_simple();
        test_routing_table_basic();
        test_ipv4_utilities_basic();
        
        std::cout << std::endl << "All simple IPv4 tests passed successfully!" << std::endl;
        std::cout << "IPv4 protocol implementation is working correctly." << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
