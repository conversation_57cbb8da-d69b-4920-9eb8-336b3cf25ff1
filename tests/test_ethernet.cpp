#include "ethernet.h"
#include "tun_tap.h"
#include <iostream>
#include <cassert>
#include <thread>
#include <cstring>
#include <chrono>

class TestTunTapInterface : public EthernetInterface {
public:
    TestTunTapInterface(const MacAddress& mac) : EthernetInterface(mac) {}
    
    bool send_frame(const EthernetFrame& frame) override {
        sent_frames_.push_back(frame);
        stats_.frames_sent++;
        stats_.bytes_sent += frame.total_size();
        return true;
    }
    
    void simulate_receive_frame(const EthernetFrame& frame) {
        handle_received_frame(frame);
    }
    
    const std::vector<EthernetFrame>& get_sent_frames() const {
        return sent_frames_;
    }
    
    void clear_sent_frames() {
        sent_frames_.clear();
    }
    
private:
    std::vector<EthernetFrame> sent_frames_;
};

void test_mac_address() {
    std::cout << "Testing MacAddress..." << std::endl;
    
    // Test default constructor
    <PERSON><PERSON><PERSON><PERSON> mac1;
    assert(mac1.is_zero());
    
    // Test string constructor
    <PERSON><PERSON><PERSON><PERSON> mac2("aa:bb:cc:dd:ee:ff");
    assert(mac2.to_string() == "aa:bb:cc:dd:ee:ff");
    
    // Test broadcast
    MacAddress broadcast("ff:ff:ff:ff:ff:ff");
    assert(broadcast.is_broadcast());
    
    // Test equality
    MacAddress mac3("aa:bb:cc:dd:ee:ff");
    assert(mac2 == mac3);
    assert(mac1 != mac2);
    
    // Test random MAC generation
    MacAddress random_mac = generate_random_mac();
    assert(!random_mac.is_zero());
    assert(!random_mac.is_broadcast());
    
    std::cout << "  Generated random MAC: " << random_mac.to_string() << std::endl;
    std::cout << "MacAddress tests passed!" << std::endl;
}

void test_ethernet_header() {
    std::cout << "Testing EthernetHeader..." << std::endl;
    
    MacAddress src_mac("aa:bb:cc:dd:ee:ff");
    MacAddress dst_mac("11:22:33:44:55:66");
    uint16_t ethertype = ETHERTYPE_IPV4;
    
    // Test constructor
    EthernetHeader header(dst_mac, src_mac, ethertype);
    assert(header.dst_mac == dst_mac);
    assert(header.src_mac == src_mac);
    assert(header.ethertype == ethertype);
    
    // Test serialization
    uint8_t buffer[ETHERNET_HEADER_SIZE];
    header.serialize(buffer);
    
    // Test deserialization
    EthernetHeader header2 = EthernetHeader::deserialize(buffer);
    assert(header2.dst_mac == dst_mac);
    assert(header2.src_mac == src_mac);
    assert(header2.ethertype == ethertype);
    
    std::cout << "  Header: " << header.to_string() << std::endl;
    std::cout << "EthernetHeader tests passed!" << std::endl;
}

void test_ethernet_frame() {
    std::cout << "Testing EthernetFrame..." << std::endl;
    
    MacAddress src_mac("aa:bb:cc:dd:ee:ff");
    MacAddress dst_mac("11:22:33:44:55:66");
    uint16_t ethertype = ETHERTYPE_IPV4;
    
    // Test payload
    std::string payload_str = "Hello, Ethernet!";
    const uint8_t* payload = reinterpret_cast<const uint8_t*>(payload_str.c_str());
    size_t payload_size = payload_str.length();
    
    // Test constructor
    EthernetFrame frame(dst_mac, src_mac, ethertype, payload, payload_size);
    assert(frame.header().dst_mac == dst_mac);
    assert(frame.header().src_mac == src_mac);
    assert(frame.header().ethertype == ethertype);
    assert(frame.payload_size() == payload_size);
    assert(memcmp(frame.payload(), payload, payload_size) == 0);
    
    // Test serialization
    auto serialized = frame.serialize();
    assert(serialized.size() == ETHERNET_HEADER_SIZE + payload_size);
    
    // Test deserialization
    EthernetFrame frame2;
    assert(frame2.deserialize(serialized.data(), serialized.size()));
    assert(frame2.header().dst_mac == dst_mac);
    assert(frame2.header().src_mac == src_mac);
    assert(frame2.header().ethertype == ethertype);
    assert(frame2.payload_size() == payload_size);
    assert(memcmp(frame2.payload(), payload, payload_size) == 0);
    
    // Test validation
    assert(frame.is_valid());
    
    // Test broadcast detection
    EthernetFrame broadcast_frame(MacAddress("ff:ff:ff:ff:ff:ff"), src_mac, ethertype, payload, payload_size);
    assert(broadcast_frame.is_broadcast());
    
    // Test multicast detection
    EthernetFrame multicast_frame(MacAddress("01:00:5e:00:00:01"), src_mac, ethertype, payload, payload_size);
    assert(multicast_frame.is_multicast());
    
    std::cout << "  Frame: " << frame.to_string() << std::endl;
    std::cout << "EthernetFrame tests passed!" << std::endl;
}

void test_ethernet_interface() {
    std::cout << "Testing EthernetInterface..." << std::endl;
    
    MacAddress local_mac("aa:bb:cc:dd:ee:ff");
    MacAddress remote_mac("11:22:33:44:55:66");
    
    TestTunTapInterface interface(local_mac);
    
    // Test MAC address
    assert(interface.get_mac_address() == local_mac);
    
    // Test frame handler
    bool frame_received = false;
    EthernetFrame received_frame;
    
    interface.set_frame_handler([&](const EthernetFrame& frame) {
        frame_received = true;
        received_frame = frame;
    });
    
    // Test sending frame
    std::string payload_str = "Test payload";
    const uint8_t* payload = reinterpret_cast<const uint8_t*>(payload_str.c_str());
    size_t payload_size = payload_str.length();
    
    EthernetFrame test_frame(remote_mac, local_mac, ETHERTYPE_IPV4, payload, payload_size);
    assert(interface.send_frame(test_frame));
    
    auto sent_frames = interface.get_sent_frames();
    assert(sent_frames.size() == 1);
    assert(sent_frames[0].header().dst_mac == remote_mac);
    
    // Test receiving frame (unicast to our MAC)
    EthernetFrame incoming_frame(local_mac, remote_mac, ETHERTYPE_IPV4, payload, payload_size);
    interface.simulate_receive_frame(incoming_frame);
    assert(frame_received);
    assert(received_frame.header().src_mac == remote_mac);
    
    // Test receiving broadcast frame
    frame_received = false;
    EthernetFrame broadcast_frame(MacAddress("ff:ff:ff:ff:ff:ff"), remote_mac, ETHERTYPE_ARP, payload, payload_size);
    interface.simulate_receive_frame(broadcast_frame);
    assert(frame_received);
    
    // Test statistics
    auto stats = interface.get_statistics();
    assert(stats.frames_sent == 1);
    assert(stats.frames_received == 2);
    assert(stats.bytes_sent > 0);
    assert(stats.bytes_received > 0);
    
    std::cout << "  Statistics: " << stats.frames_sent << " sent, " 
              << stats.frames_received << " received" << std::endl;
    std::cout << "EthernetInterface tests passed!" << std::endl;
}

void test_ethertype_utilities() {
    std::cout << "Testing EtherType utilities..." << std::endl;
    
    assert(ethertype_to_string(ETHERTYPE_IPV4) == "IPv4");
    assert(ethertype_to_string(ETHERTYPE_ARP) == "ARP");
    assert(ethertype_to_string(ETHERTYPE_IPV6) == "IPv6");
    assert(ethertype_to_string(0x1234) == "0x1234");
    
    assert(is_valid_mac_address("aa:bb:cc:dd:ee:ff"));
    assert(!is_valid_mac_address("aa:bb:cc:dd:ee"));
    assert(!is_valid_mac_address("aa:bb:cc:dd:ee:gg"));
    assert(!is_valid_mac_address("aa-bb-cc-dd-ee-ff"));
    
    std::cout << "EtherType utilities tests passed!" << std::endl;
}

void test_frame_dump() {
    std::cout << "Testing frame dump functionality..." << std::endl;
    
    MacAddress src_mac("aa:bb:cc:dd:ee:ff");
    MacAddress dst_mac("11:22:33:44:55:66");
    
    std::string payload_str = "Hello, World! This is a test payload for Ethernet frame.";
    const uint8_t* payload = reinterpret_cast<const uint8_t*>(payload_str.c_str());
    size_t payload_size = payload_str.length();
    
    EthernetFrame frame(dst_mac, src_mac, ETHERTYPE_IPV4, payload, payload_size);
    
    std::cout << "  Frame dump:" << std::endl;
    frame.dump();
    
    std::cout << "Frame dump test completed!" << std::endl;
}

int main() {
    std::cout << "=== Ethernet Layer Tests ===" << std::endl;
    
    try {
        test_mac_address();
        test_ethernet_header();
        test_ethernet_frame();
        test_ethernet_interface();
        test_ethertype_utilities();
        test_frame_dump();
        
        std::cout << std::endl << "All Ethernet tests passed successfully!" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
