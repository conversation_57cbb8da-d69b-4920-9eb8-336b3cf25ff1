#include "arp.h"
#include "ethernet.h"
#include <iostream>
#include <cassert>

class SimpleTestInterface : public EthernetInterface {
public:
    SimpleTestInterface(const MacAddress& mac) : EthernetInterface(mac) {}
    
    bool send_frame(const EthernetFrame& frame) override {
        sent_frames_.push_back(frame);
        stats_.frames_sent++;
        stats_.bytes_sent += frame.total_size();
        return true;
    }
    
    const std::vector<EthernetFrame>& get_sent_frames() const {
        return sent_frames_;
    }
    
    void clear_sent_frames() {
        sent_frames_.clear();
    }
    
private:
    std::vector<EthernetFrame> sent_frames_;
};

void test_arp_header_simple() {
    std::cout << "Testing ARP header (simple)..." << std::endl;
    
    MacAddress sender_mac("aa:bb:cc:dd:ee:ff");
    IpAddress sender_ip("***********");
    IpAddress target_ip("***********");
    
    // Test ARP request
    ArpHeader request(ARP_OP_REQUEST, sender_mac, sender_ip, MacAddress(), target_ip);
    assert(request.is_request());
    assert(request.is_valid());
    
    // Test serialization/deserialization
    std::vector<uint8_t> buffer(ArpHeader::size());
    request.serialize(buffer.data());
    
    ArpHeader request2 = ArpHeader::deserialize(buffer.data());
    assert(request2.operation == request.operation);
    assert(request2.sender_mac == request.sender_mac);
    assert(request2.sender_ip == request.sender_ip);
    assert(request2.target_ip == request.target_ip);
    
    std::cout << "  " << request.to_string() << std::endl;
    std::cout << "ARP header simple tests passed!" << std::endl;
}

void test_arp_table_simple() {
    std::cout << "Testing ARP table (simple)..." << std::endl;
    
    ArpTable table;
    
    IpAddress ip1("***********");
    MacAddress mac1("aa:bb:cc:dd:ee:ff");
    
    // Test basic operations
    table.add_entry(ip1, mac1);
    assert(table.size() == 1);
    assert(table.has_entry(ip1));
    
    MacAddress found_mac;
    assert(table.lookup(ip1, found_mac));
    assert(found_mac == mac1);
    
    // Test removal
    assert(table.remove_entry(ip1));
    assert(table.size() == 0);
    assert(!table.has_entry(ip1));
    
    std::cout << "ARP table simple tests passed!" << std::endl;
}

void test_arp_protocol_no_thread() {
    std::cout << "Testing ARP protocol (no background thread)..." << std::endl;
    
    MacAddress local_mac("aa:bb:cc:dd:ee:ff");
    IpAddress local_ip("***********");
    
    SimpleTestInterface interface(local_mac);
    
    // Create ARP protocol but don't use background features
    {
        ArpProtocol arp(interface, local_ip);
        
        // Test sending ARP request
        IpAddress target_ip("***********");
        assert(arp.send_request(target_ip));
        
        auto sent_frames = interface.get_sent_frames();
        assert(sent_frames.size() == 1);
        
        const auto& frame = sent_frames[0];
        assert(frame.header().ethertype == ETHERTYPE_ARP);
        assert(frame.header().dst_mac.is_broadcast());
        
        // Verify ARP request content
        ArpHeader arp_header = ArpHeader::deserialize(frame.payload());
        assert(arp_header.is_request());
        assert(arp_header.sender_ip == local_ip);
        assert(arp_header.target_ip == target_ip);
        
        std::cout << "  Sent: " << arp_header.to_string() << std::endl;
        
        // Test manual ARP table operations
        arp.get_table().add_entry(target_ip, MacAddress("bb:bb:bb:bb:bb:bb"));
        
        MacAddress resolved_mac;
        assert(arp.get_table().lookup(target_ip, resolved_mac));
        assert(resolved_mac == MacAddress("bb:bb:bb:bb:bb:bb"));
        
        // Test statistics
        auto stats = arp.get_statistics();
        assert(stats.requests_sent == 1);
        
        std::cout << "  Statistics: " << stats.requests_sent << " requests sent" << std::endl;
    } // ARP destructor called here, should clean up thread
    
    std::cout << "ARP protocol no-thread tests passed!" << std::endl;
}

void test_arp_frame_handling() {
    std::cout << "Testing ARP frame handling..." << std::endl;
    
    MacAddress local_mac("aa:bb:cc:dd:ee:ff");
    IpAddress local_ip("***********");
    MacAddress remote_mac("11:22:33:44:55:66");
    IpAddress remote_ip("***********");
    
    SimpleTestInterface interface(local_mac);
    
    {
        ArpProtocol arp(interface, local_ip);
        
        // Create an ARP request frame targeting our IP
        ArpHeader request(ARP_OP_REQUEST, remote_mac, remote_ip, MacAddress(), local_ip);
        std::vector<uint8_t> request_data(ArpHeader::size());
        request.serialize(request_data.data());
        
        EthernetFrame request_frame(MacAddress("ff:ff:ff:ff:ff:ff"), remote_mac, ETHERTYPE_ARP,
                                   request_data.data(), request_data.size());
        
        // Handle the frame
        arp.handle_ethernet_frame(request_frame);
        
        // Should have added the sender to ARP table
        MacAddress found_mac;
        assert(arp.get_table().lookup(remote_ip, found_mac));
        assert(found_mac == remote_mac);
        
        // Check statistics
        auto stats = arp.get_statistics();
        assert(stats.requests_received == 1);
        
        std::cout << "  Handled ARP request and updated table" << std::endl;
    }
    
    std::cout << "ARP frame handling tests passed!" << std::endl;
}

void test_arp_utilities_simple() {
    std::cout << "Testing ARP utilities..." << std::endl;
    
    assert(arp_operation_to_string(ARP_OP_REQUEST) == "Request");
    assert(arp_operation_to_string(ARP_OP_REPLY) == "Reply");
    
    // Test packet validation
    ArpHeader valid_header(ARP_OP_REQUEST, MacAddress("aa:bb:cc:dd:ee:ff"), 
                          IpAddress("***********"), MacAddress(), IpAddress("***********"));
    std::vector<uint8_t> valid_data(ArpHeader::size());
    valid_header.serialize(valid_data.data());
    
    assert(is_valid_arp_packet(valid_data.data(), valid_data.size()));
    assert(!is_valid_arp_packet(valid_data.data(), 10)); // Too small
    
    std::cout << "ARP utilities tests passed!" << std::endl;
}

int main() {
    std::cout << "=== Simple ARP Protocol Tests ===" << std::endl;
    std::cout << "Testing ARP without background threads to avoid hanging" << std::endl;
    std::cout << std::endl;
    
    try {
        test_arp_header_simple();
        test_arp_table_simple();
        test_arp_protocol_no_thread();
        test_arp_frame_handling();
        test_arp_utilities_simple();
        
        std::cout << std::endl << "All simple ARP tests passed successfully!" << std::endl;
        std::cout << "ARP protocol implementation is working correctly." << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
