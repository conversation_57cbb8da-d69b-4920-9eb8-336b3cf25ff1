#include "tcp.h"
#include <iostream>
#include <cassert>
#include <cstring>
#include <thread>
#include <chrono>

void test_tcp_connection_states() {
    std::cout << "Testing TCP connection states..." << std::endl;
    
    // Test state string conversion
    assert(tcp_state_to_string(TcpState::CLOSED) == "CLOSED");
    assert(tcp_state_to_string(TcpState::LISTEN) == "LISTEN");
    assert(tcp_state_to_string(TcpState::SYN_SENT) == "SYN_SENT");
    assert(tcp_state_to_string(TcpState::SYN_RECEIVED) == "SYN_RECEIVED");
    assert(tcp_state_to_string(TcpState::ESTABLISHED) == "ESTABLISHED");
    assert(tcp_state_to_string(TcpState::FIN_WAIT_1) == "FIN_WAIT_1");
    assert(tcp_state_to_string(TcpState::FIN_WAIT_2) == "FIN_WAIT_2");
    assert(tcp_state_to_string(TcpState::CLOSE_WAIT) == "CLOSE_WAIT");
    assert(tcp_state_to_string(TcpState::CLOSING) == "CLOSING");
    assert(tcp_state_to_string(TcpState::LAST_ACK) == "LAST_ACK");
    assert(tcp_state_to_string(TcpState::TIME_WAIT) == "TIME_WAIT");
    
    std::cout << "TCP connection states tests passed!" << std::endl;
}

void test_tcp_sequence_numbers() {
    std::cout << "Testing TCP sequence number arithmetic..." << std::endl;
    
    // Test sequence number comparison
    assert(sequence_less_than(100, 200));
    assert(!sequence_less_than(200, 100));
    assert(!sequence_less_than(100, 100));
    
    assert(sequence_less_equal(100, 200));
    assert(!sequence_less_equal(200, 100));
    assert(sequence_less_equal(100, 100));
    
    // Test wraparound cases
    uint32_t high = 0xFFFFFFF0;
    uint32_t low = 0x00000010;
    assert(sequence_less_than(high, low)); // Wraparound case
    assert(!sequence_less_than(low, high));
    
    // Test sequence arithmetic
    assert(sequence_add(100, 50) == 150);
    assert(sequence_subtract(150, 50) == 100);
    
    std::cout << "TCP sequence number tests passed!" << std::endl;
}

void test_tcp_options() {
    std::cout << "Testing TCP options..." << std::endl;
    
    // Test default options
    TcpOptions options1;
    assert(options1.mss == TCP_DEFAULT_MSS);
    assert(options1.window_scale == 0);
    assert(!options1.sack_permitted);
    assert(!options1.timestamp_enabled);
    
    // Test options with values
    TcpOptions options2;
    options2.mss = 1200;
    options2.window_scale = 3;
    options2.sack_permitted = true;
    options2.timestamp_enabled = true;
    options2.timestamp_value = 12345;
    options2.timestamp_echo_reply = 67890;
    
    // Test serialization
    auto serialized = options2.serialize();
    assert(!serialized.empty());
    
    // Test deserialization
    TcpOptions options3;
    assert(options3.deserialize(serialized.data(), serialized.size()));
    assert(options3.mss == 1200);
    assert(options3.window_scale == 3);
    assert(options3.sack_permitted);
    assert(options3.timestamp_enabled);
    assert(options3.timestamp_value == 12345);
    assert(options3.timestamp_echo_reply == 67890);
    
    std::cout << "  " << options2.to_string() << std::endl;
    std::cout << "TCP options tests passed!" << std::endl;
}

void test_tcp_segment_with_options() {
    std::cout << "Testing TCP segment with options..." << std::endl;
    
    // Create TCP header
    TcpHeader header(12345, 80, 1000, 2000, TCP_FLAG_SYN, TCP_MAX_WINDOW_SIZE);
    
    // Create options
    TcpOptions options;
    options.mss = 1200;
    options.window_scale = 2;
    options.sack_permitted = true;
    
    // Create segment with options
    std::string payload = "Hello TCP with options!";
    TcpSegment segment(header, options, 
                      reinterpret_cast<const uint8_t*>(payload.c_str()), payload.length());
    
    // Test that options are included
    assert(segment.options().mss == 1200);
    assert(segment.options().window_scale == 2);
    assert(segment.options().sack_permitted);
    
    // Test serialization with options
    IpAddress src_ip("********");
    IpAddress dst_ip("********");
    auto serialized = segment.serialize(src_ip, dst_ip);
    
    // Should be larger than minimum header + payload due to options
    assert(serialized.size() > TCP_HEADER_MIN_SIZE + payload.length());
    
    // Test deserialization
    TcpSegment segment2;
    assert(segment2.deserialize(serialized.data(), serialized.size(), src_ip, dst_ip));
    assert(segment2.options().mss == 1200);
    assert(segment2.options().window_scale == 2);
    assert(segment2.options().sack_permitted);
    assert(segment2.payload_size() == payload.length());
    assert(std::memcmp(segment2.payload(), payload.c_str(), payload.length()) == 0);
    
    std::cout << "  " << segment.to_string() << std::endl;
    std::cout << "TCP segment with options tests passed!" << std::endl;
}

void test_tcp_handshake_segments() {
    std::cout << "Testing TCP handshake segments..." << std::endl;
    
    IpAddress client_ip("***********00");
    IpAddress server_ip("***********");
    uint16_t client_port = 12345;
    uint16_t server_port = 80;
    
    uint32_t client_isn = generate_initial_sequence_number();
    uint32_t server_isn = generate_initial_sequence_number();
    
    // Step 1: Client sends SYN
    TcpSegment syn_segment(client_port, server_port, client_isn, 0, 
                          TCP_FLAG_SYN, TCP_MAX_WINDOW_SIZE);
    
    assert(syn_segment.header().has_flag(TCP_FLAG_SYN));
    assert(!syn_segment.header().has_flag(TCP_FLAG_ACK));
    assert(syn_segment.get_sequence_number() == client_isn);
    assert(syn_segment.get_acknowledgment_number() == 0);
    
    // Step 2: Server responds with SYN-ACK
    TcpSegment syn_ack_segment(server_port, client_port, server_isn, client_isn + 1,
                              TCP_FLAG_SYN | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
    
    assert(syn_ack_segment.header().has_flag(TCP_FLAG_SYN));
    assert(syn_ack_segment.header().has_flag(TCP_FLAG_ACK));
    assert(syn_ack_segment.get_sequence_number() == server_isn);
    assert(syn_ack_segment.get_acknowledgment_number() == client_isn + 1);
    
    // Step 3: Client sends ACK
    TcpSegment ack_segment(client_port, server_port, client_isn + 1, server_isn + 1,
                          TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
    
    assert(!ack_segment.header().has_flag(TCP_FLAG_SYN));
    assert(ack_segment.header().has_flag(TCP_FLAG_ACK));
    assert(ack_segment.get_sequence_number() == client_isn + 1);
    assert(ack_segment.get_acknowledgment_number() == server_isn + 1);
    
    std::cout << "  SYN: " << syn_segment.to_string() << std::endl;
    std::cout << "  SYN-ACK: " << syn_ack_segment.to_string() << std::endl;
    std::cout << "  ACK: " << ack_segment.to_string() << std::endl;
    std::cout << "TCP handshake segments tests passed!" << std::endl;
}

void test_tcp_data_transfer() {
    std::cout << "Testing TCP data transfer..." << std::endl;
    
    uint16_t client_port = 12345;
    uint16_t server_port = 80;
    uint32_t seq = 1000;
    uint32_t ack = 2000;
    
    // Test data segment
    std::string data = "GET / HTTP/1.1\r\nHost: example.com\r\n\r\n";
    TcpSegment data_segment(client_port, server_port, seq, ack,
                           TCP_FLAG_PSH | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE,
                           reinterpret_cast<const uint8_t*>(data.c_str()), data.length());
    
    assert(data_segment.header().has_flag(TCP_FLAG_PSH));
    assert(data_segment.header().has_flag(TCP_FLAG_ACK));
    assert(data_segment.payload_size() == data.length());
    assert(std::memcmp(data_segment.payload(), data.c_str(), data.length()) == 0);
    
    // Test ACK response
    TcpSegment ack_response(server_port, client_port, ack, seq + data.length(),
                           TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
    
    assert(ack_response.header().has_flag(TCP_FLAG_ACK));
    assert(ack_response.get_acknowledgment_number() == seq + data.length());
    assert(ack_response.payload_size() == 0);
    
    std::cout << "  Data: " << data_segment.to_string() << std::endl;
    std::cout << "  ACK: " << ack_response.to_string() << std::endl;
    std::cout << "TCP data transfer tests passed!" << std::endl;
}

void test_tcp_connection_teardown() {
    std::cout << "Testing TCP connection teardown..." << std::endl;
    
    uint16_t client_port = 12345;
    uint16_t server_port = 80;
    uint32_t client_seq = 1000;
    uint32_t server_seq = 2000;
    
    // Step 1: Client sends FIN
    TcpSegment fin_segment(client_port, server_port, client_seq, server_seq,
                          TCP_FLAG_FIN | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
    
    assert(fin_segment.header().has_flag(TCP_FLAG_FIN));
    assert(fin_segment.header().has_flag(TCP_FLAG_ACK));
    
    // Step 2: Server sends ACK
    TcpSegment ack_segment(server_port, client_port, server_seq, client_seq + 1,
                          TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
    
    assert(ack_segment.header().has_flag(TCP_FLAG_ACK));
    assert(!ack_segment.header().has_flag(TCP_FLAG_FIN));
    
    // Step 3: Server sends FIN
    TcpSegment server_fin_segment(server_port, client_port, server_seq, client_seq + 1,
                                 TCP_FLAG_FIN | TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
    
    assert(server_fin_segment.header().has_flag(TCP_FLAG_FIN));
    assert(server_fin_segment.header().has_flag(TCP_FLAG_ACK));
    
    // Step 4: Client sends final ACK
    TcpSegment final_ack_segment(client_port, server_port, client_seq + 1, server_seq + 1,
                                TCP_FLAG_ACK, TCP_MAX_WINDOW_SIZE);
    
    assert(final_ack_segment.header().has_flag(TCP_FLAG_ACK));
    assert(!final_ack_segment.header().has_flag(TCP_FLAG_FIN));
    
    std::cout << "  Client FIN: " << fin_segment.to_string() << std::endl;
    std::cout << "  Server ACK: " << ack_segment.to_string() << std::endl;
    std::cout << "  Server FIN: " << server_fin_segment.to_string() << std::endl;
    std::cout << "  Client ACK: " << final_ack_segment.to_string() << std::endl;
    std::cout << "TCP connection teardown tests passed!" << std::endl;
}

int main() {
    std::cout << "=== Comprehensive TCP Protocol Tests ===" << std::endl;
    std::cout << "Testing TCP implementation with connection management" << std::endl;
    std::cout << std::endl;
    
    try {
        test_tcp_connection_states();
        test_tcp_sequence_numbers();
        test_tcp_options();
        test_tcp_segment_with_options();
        test_tcp_handshake_segments();
        test_tcp_data_transfer();
        test_tcp_connection_teardown();
        
        std::cout << std::endl << "All comprehensive TCP tests passed successfully!" << std::endl;
        std::cout << "TCP protocol implementation is ready for connection management." << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
