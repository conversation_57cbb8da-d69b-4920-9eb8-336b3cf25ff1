#include "icmp.h"
#include <iostream>
#include <cassert>
#include <cstring>

class TestEthernetInterface : public EthernetInterface {
public:
    TestEthernetInterface(const MacAddress& mac) : EthernetInterface(mac) {}
    
    bool send_frame(const EthernetFrame& frame) override {
        sent_frames_.push_back(frame);
        stats_.frames_sent++;
        stats_.bytes_sent += frame.total_size();
        return true;
    }
    
    const std::vector<EthernetFrame>& get_sent_frames() const {
        return sent_frames_;
    }
    
    void clear_sent_frames() {
        sent_frames_.clear();
    }
    
private:
    std::vector<EthernetFrame> sent_frames_;
};

void test_icmp_header() {
    std::cout << "Testing ICMP header..." << std::endl;
    
    // Test default constructor
    IcmpHeader header1;
    assert(header1.type == 0);
    assert(header1.code == 0);
    assert(header1.checksum == 0);
    assert(header1.rest == 0);
    
    // Test parameterized constructor
    IcmpHeader header2(ICMP_TYPE_ECHO_REQUEST, 0, 0x12345678);
    assert(header2.type == ICMP_TYPE_ECHO_REQUEST);
    assert(header2.code == 0);
    assert(header2.rest == 0x12345678);
    
    // Test echo data
    header2.set_echo_data(0x1234, 0x5678);
    assert(header2.get_identifier() == 0x1234);
    assert(header2.get_sequence() == 0x5678);
    
    // Test type checking
    assert(header2.is_echo_request());
    assert(!header2.is_echo_reply());
    assert(!header2.is_error_message());
    
    // Test serialization
    std::vector<uint8_t> buffer(ICMP_HEADER_SIZE);
    header2.serialize(buffer.data());
    
    IcmpHeader header3 = IcmpHeader::deserialize(buffer.data());
    assert(header3.type == header2.type);
    assert(header3.code == header2.code);
    assert(header3.rest == header2.rest);
    
    std::cout << "  " << header2.to_string() << std::endl;
    std::cout << "ICMP header tests passed!" << std::endl;
}

void test_icmp_message() {
    std::cout << "Testing ICMP message..." << std::endl;
    
    // Test echo request creation
    std::string payload_str = "Hello ICMP!";
    const uint8_t* payload_data = reinterpret_cast<const uint8_t*>(payload_str.c_str());
    size_t payload_size = payload_str.length();
    
    IcmpMessage msg1 = IcmpMessage::create_echo_request(0x1234, 0x5678, 
                                                       payload_data, payload_size);
    
    assert(msg1.header().is_echo_request());
    assert(msg1.header().get_identifier() == 0x1234);
    assert(msg1.header().get_sequence() == 0x5678);
    assert(msg1.payload_size() == payload_size);
    assert(std::memcmp(msg1.payload(), payload_data, payload_size) == 0);
    
    // Test serialization
    auto serialized = msg1.serialize();
    assert(serialized.size() == ICMP_HEADER_SIZE + payload_size);
    
    // Test deserialization
    IcmpMessage msg2;
    assert(msg2.deserialize(serialized.data(), serialized.size()));
    assert(msg2.header().type == msg1.header().type);
    assert(msg2.header().get_identifier() == msg1.header().get_identifier());
    assert(msg2.header().get_sequence() == msg1.header().get_sequence());
    assert(msg2.payload_size() == payload_size);
    
    // Test validity
    assert(msg1.is_valid());
    assert(msg2.is_valid());
    
    // Test echo reply creation
    IcmpMessage reply = IcmpMessage::create_echo_reply(0x1234, 0x5678,
                                                      payload_data, payload_size);
    assert(reply.header().is_echo_reply());
    assert(reply.header().get_identifier() == 0x1234);
    assert(reply.header().get_sequence() == 0x5678);
    
    std::cout << "  Request: " << msg1.to_string() << std::endl;
    std::cout << "  Reply: " << reply.to_string() << std::endl;
    std::cout << "ICMP message tests passed!" << std::endl;
}

void test_icmp_checksum() {
    std::cout << "Testing ICMP checksum..." << std::endl;
    
    // Create a simple ICMP message
    std::string data = "test";
    IcmpMessage msg = IcmpMessage::create_echo_request(1, 1, 
                                                      reinterpret_cast<const uint8_t*>(data.c_str()),
                                                      data.length());
    
    // Serialize (this calculates checksum)
    auto serialized = msg.serialize();
    
    // Deserialize and verify
    IcmpMessage msg2;
    assert(msg2.deserialize(serialized.data(), serialized.size()));
    assert(msg2.is_valid());
    
    // Test checksum verification
    assert(msg2.header().verify_checksum(msg2.payload(), msg2.payload_size()));
    
    std::cout << "  Checksum: 0x" << std::hex << msg2.header().checksum << std::dec << std::endl;
    std::cout << "ICMP checksum tests passed!" << std::endl;
}

void test_icmp_protocol_basic() {
    std::cout << "Testing ICMP protocol basic..." << std::endl;
    
    MacAddress local_mac("aa:bb:cc:dd:ee:ff");
    IpAddress local_ip("***********");
    IpAddress target_ip("***********");
    
    TestEthernetInterface interface(local_mac);
    IcmpProtocol icmp(interface, local_ip);
    
    // Test sending echo request
    std::string data = "ping test";
    bool sent = icmp.send_echo_request(target_ip, 1, 1,
                                      reinterpret_cast<const uint8_t*>(data.c_str()),
                                      data.length());
    assert(sent);
    
    auto sent_frames = interface.get_sent_frames();
    assert(sent_frames.size() == 1);
    
    const auto& frame = sent_frames[0];
    assert(frame.header().ethertype == ETHERTYPE_IPV4);
    
    // Verify IPv4 packet
    Ipv4Packet ipv4_packet;
    assert(ipv4_packet.deserialize(frame.payload(), frame.payload_size()));
    assert(ipv4_packet.header().protocol == IPV4_PROTOCOL_ICMP);
    assert(ipv4_packet.header().source_ip == local_ip);
    assert(ipv4_packet.header().destination_ip == target_ip);
    
    // Verify ICMP message
    IcmpMessage icmp_msg;
    assert(icmp_msg.deserialize(ipv4_packet.payload(), ipv4_packet.payload_size()));
    assert(icmp_msg.header().is_echo_request());
    assert(icmp_msg.payload_size() == data.length());
    
    // Test statistics
    auto stats = icmp.get_statistics();
    assert(stats.echo_requests_sent == 1);
    
    std::cout << "  Sent: " << icmp_msg.to_string() << std::endl;
    std::cout << "ICMP protocol basic tests passed!" << std::endl;
}

void test_icmp_echo_handling() {
    std::cout << "Testing ICMP echo handling..." << std::endl;
    
    MacAddress local_mac("aa:bb:cc:dd:ee:ff");
    IpAddress local_ip("***********");
    IpAddress remote_ip("***********");
    
    TestEthernetInterface interface(local_mac);
    IcmpProtocol icmp(interface, local_ip);
    
    // Set up handlers
    bool echo_request_received = false;
    bool echo_reply_received = false;
    
    icmp.set_echo_request_handler([&](const IcmpMessage& msg, const IpAddress& source) {
        echo_request_received = true;
        assert(source == remote_ip);
        assert(msg.header().is_echo_request());
    });

    icmp.set_echo_reply_handler([&](const IcmpMessage& msg, const IpAddress& source) {
        echo_reply_received = true;
        assert(source == remote_ip);
        assert(msg.header().is_echo_reply());
    });
    
    // Create and handle echo request
    std::string data = "ping data";
    IcmpMessage request = IcmpMessage::create_echo_request(0x1234, 0x5678,
                                                          reinterpret_cast<const uint8_t*>(data.c_str()),
                                                          data.length());
    
    auto icmp_data = request.serialize();
    Ipv4Packet ipv4_packet(remote_ip, local_ip, IPV4_PROTOCOL_ICMP,
                           icmp_data.data(), icmp_data.size());
    
    interface.clear_sent_frames();
    icmp.handle_ipv4_packet(ipv4_packet);
    
    // Should have received echo request and sent reply
    assert(echo_request_received);
    
    auto sent_frames = interface.get_sent_frames();
    assert(sent_frames.size() == 1);
    
    // Verify echo reply was sent
    const auto& reply_frame = sent_frames[0];
    Ipv4Packet reply_ipv4;
    assert(reply_ipv4.deserialize(reply_frame.payload(), reply_frame.payload_size()));
    
    IcmpMessage reply_icmp;
    assert(reply_icmp.deserialize(reply_ipv4.payload(), reply_ipv4.payload_size()));
    assert(reply_icmp.header().is_echo_reply());
    assert(reply_icmp.header().get_identifier() == 0x1234);
    assert(reply_icmp.header().get_sequence() == 0x5678);
    
    // Simulate handling the reply
    icmp.handle_ipv4_packet(reply_ipv4);
    assert(echo_reply_received);
    
    // Test statistics
    auto stats = icmp.get_statistics();
    assert(stats.echo_requests_received == 1);
    assert(stats.echo_replies_sent == 1);
    assert(stats.echo_replies_received == 1);
    
    std::cout << "  Request handled and reply sent" << std::endl;
    std::cout << "ICMP echo handling tests passed!" << std::endl;
}

void test_icmp_error_messages() {
    std::cout << "Testing ICMP error messages..." << std::endl;
    
    // Test destination unreachable message
    std::vector<uint8_t> original_packet = {0x45, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x40, 0x00};
    IcmpMessage dest_unreach = IcmpMessage::create_dest_unreachable(
        ICMP_CODE_HOST_UNREACHABLE, original_packet.data(), original_packet.size());
    
    assert(dest_unreach.header().type == ICMP_TYPE_DEST_UNREACHABLE);
    assert(dest_unreach.header().code == ICMP_CODE_HOST_UNREACHABLE);
    assert(dest_unreach.header().is_error_message());
    assert(dest_unreach.payload_size() == 8); // Limited to 8 bytes
    
    // Test serialization/deserialization
    auto serialized = dest_unreach.serialize();
    IcmpMessage dest_unreach2;
    assert(dest_unreach2.deserialize(serialized.data(), serialized.size()));
    assert(dest_unreach2.is_valid());
    
    std::cout << "  " << dest_unreach.to_string() << std::endl;
    std::cout << "ICMP error message tests passed!" << std::endl;
}

void test_icmp_utilities() {
    std::cout << "Testing ICMP utilities..." << std::endl;
    
    // Test type strings
    assert(icmp_type_to_string(ICMP_TYPE_ECHO_REQUEST) == "Echo Request");
    assert(icmp_type_to_string(ICMP_TYPE_ECHO_REPLY) == "Echo Reply");
    assert(icmp_type_to_string(ICMP_TYPE_DEST_UNREACHABLE) == "Destination Unreachable");
    assert(icmp_type_to_string(255) == "Type 255");
    
    // Test code strings
    assert(icmp_code_to_string(ICMP_TYPE_DEST_UNREACHABLE, ICMP_CODE_HOST_UNREACHABLE) == "Host Unreachable");
    assert(icmp_code_to_string(ICMP_TYPE_TIME_EXCEEDED, ICMP_CODE_TTL_EXCEEDED) == "TTL Exceeded");
    
    // Test message validation
    IcmpMessage valid_msg = IcmpMessage::create_echo_request(1, 1, nullptr, 0);
    auto valid_data = valid_msg.serialize();
    assert(is_valid_icmp_message(valid_data.data(), valid_data.size()));
    assert(!is_valid_icmp_message(valid_data.data(), 3)); // Too small
    
    std::cout << "ICMP utilities tests passed!" << std::endl;
}

int main() {
    std::cout << "=== ICMP Protocol Tests ===" << std::endl;
    
    try {
        test_icmp_header();
        test_icmp_message();
        test_icmp_checksum();
        test_icmp_protocol_basic();
        test_icmp_echo_handling();
        test_icmp_error_messages();
        test_icmp_utilities();
        
        std::cout << std::endl << "All ICMP tests passed successfully!" << std::endl;
        std::cout << "ICMP protocol implementation is working correctly." << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
