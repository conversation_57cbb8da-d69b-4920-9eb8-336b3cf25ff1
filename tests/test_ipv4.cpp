#include "ipv4.h"
#include <iostream>
#include <cassert>
#include <cstring>

void test_ipv4_header() {
    std::cout << "Testing IPv4 header..." << std::endl;
    
    IpAddress src("***********");
    IpAddress dst("***********");
    
    // Test default constructor
    Ipv4Header header1;
    assert(header1.get_version() == 4);
    assert(header1.get_ihl() == 5);
    assert(header1.get_header_length() == 20);
    assert(header1.time_to_live == IPV4_DEFAULT_TTL);
    
    // Test parameterized constructor
    Ipv4Header header2(src, dst, IPV4_PROTOCOL_TCP, 100, 64);
    assert(header2.source_ip == src);
    assert(header2.destination_ip == dst);
    assert(header2.protocol == IPV4_PROTOCOL_TCP);
    assert(header2.time_to_live == 64);
    assert(header2.total_length == 120); // 20 + 100
    
    // Test serialization/deserialization
    std::vector<uint8_t> buffer(20);
    header2.calculate_checksum();
    header2.serialize(buffer.data());
    
    Ipv4Header header3 = Ipv4Header::deserialize(buffer.data());
    assert(header3.source_ip == src);
    assert(header3.destination_ip == dst);
    assert(header3.protocol == IPV4_PROTOCOL_TCP);
    assert(header3.total_length == 120);
    
    // Test checksum verification
    assert(header3.verify_checksum());
    
    // Test validity
    assert(header2.is_valid());
    assert(header3.is_valid());
    
    // Test fragmentation flags
    assert(!header2.is_fragmented());
    assert(!header2.has_more_fragments());
    
    std::cout << "  " << header2.to_string() << std::endl;
    std::cout << "IPv4 header tests passed!" << std::endl;
}

void test_ipv4_packet() {
    std::cout << "Testing IPv4 packet..." << std::endl;
    
    IpAddress src("********");
    IpAddress dst("********");
    
    // Test payload data
    std::string payload_str = "Hello, IPv4!";
    const uint8_t* payload_data = reinterpret_cast<const uint8_t*>(payload_str.c_str());
    size_t payload_size = payload_str.length();
    
    // Test packet creation
    Ipv4Packet packet1(src, dst, IPV4_PROTOCOL_UDP, payload_data, payload_size);
    assert(packet1.header().source_ip == src);
    assert(packet1.header().destination_ip == dst);
    assert(packet1.header().protocol == IPV4_PROTOCOL_UDP);
    assert(packet1.payload_size() == payload_size);
    assert(packet1.total_size() == 20 + payload_size);
    
    // Test serialization
    auto serialized = packet1.serialize();
    assert(serialized.size() == 20 + payload_size);
    
    // Test deserialization
    Ipv4Packet packet2;
    assert(packet2.deserialize(serialized.data(), serialized.size()));
    assert(packet2.header().source_ip == src);
    assert(packet2.header().destination_ip == dst);
    assert(packet2.payload_size() == payload_size);
    
    // Verify payload content
    assert(std::memcmp(packet2.payload(), payload_data, payload_size) == 0);
    
    // Test validity
    assert(packet1.is_valid());
    assert(packet2.is_valid());
    
    std::cout << "  " << packet1.to_string() << std::endl;
    std::cout << "IPv4 packet tests passed!" << std::endl;
}

void test_route_entry() {
    std::cout << "Testing route entry..." << std::endl;
    
    IpAddress network("***********");
    IpAddress netmask("*************");
    IpAddress gateway("***********");
    
    RouteEntry route(network, netmask, gateway, "eth0", 10);
    
    // Test matching
    assert(route.matches(IpAddress("***********00")));
    assert(route.matches(IpAddress("***********")));
    assert(!route.matches(IpAddress("***********")));
    assert(!route.matches(IpAddress("********")));
    
    // Test default route
    RouteEntry default_route(IpAddress("0.0.0.0"), IpAddress("0.0.0.0"), 
                            gateway, "eth0", 100);
    assert(default_route.matches(IpAddress("*******")));
    assert(default_route.matches(IpAddress("***********")));
    
    std::cout << "  " << route.to_string() << std::endl;
    std::cout << "Route entry tests passed!" << std::endl;
}

void test_routing_table() {
    std::cout << "Testing routing table..." << std::endl;
    
    RoutingTable table;
    assert(table.size() == 0);
    
    // Add routes
    RouteEntry route1(IpAddress("***********"), IpAddress("*************"),
                     IpAddress("***********"), "eth0", 10);
    RouteEntry route2(IpAddress("10.0.0.0"), IpAddress("*********"),
                     IpAddress("********"), "eth1", 20);
    
    table.add_route(route1);
    table.add_route(route2);
    assert(table.size() == 2);
    
    // Test route lookup
    RouteEntry found_route;
    assert(table.lookup_route(IpAddress("***********00"), found_route));
    assert(found_route.network == route1.network);
    
    assert(table.lookup_route(IpAddress("********00"), found_route));
    assert(found_route.network == route2.network);
    
    assert(!table.lookup_route(IpAddress("**********"), found_route));
    
    // Test next hop calculation
    IpAddress next_hop = table.get_next_hop(IpAddress("***********00"));
    assert(next_hop == IpAddress("***********"));
    
    // Test default route
    table.set_default_route(IpAddress("***********"), "eth0");
    assert(table.has_default_route());
    assert(table.size() == 3);
    
    // Should now find route for any destination
    assert(table.lookup_route(IpAddress("*******"), found_route));
    
    // Test route removal
    assert(table.remove_route(route1.network, route1.netmask));
    assert(table.size() == 2);
    assert(!table.lookup_route(IpAddress("***********00"), found_route) ||
           found_route.network.addr == 0); // Should match default route
    
    std::cout << "Routing table tests passed!" << std::endl;
}

void test_ipv4_utilities() {
    std::cout << "Testing IPv4 utilities..." << std::endl;
    
    // Test protocol string conversion
    assert(ipv4_protocol_to_string(IPV4_PROTOCOL_ICMP) == "ICMP");
    assert(ipv4_protocol_to_string(IPV4_PROTOCOL_TCP) == "TCP");
    assert(ipv4_protocol_to_string(IPV4_PROTOCOL_UDP) == "UDP");
    assert(ipv4_protocol_to_string(99) == "Protocol 99");
    
    // Test packet validation
    Ipv4Packet valid_packet(IpAddress("*******"), IpAddress("*******"), 
                           IPV4_PROTOCOL_ICMP, nullptr, 0);
    auto serialized = valid_packet.serialize();
    assert(is_valid_ipv4_packet(serialized.data(), serialized.size()));
    assert(!is_valid_ipv4_packet(serialized.data(), 10)); // Too small
    
    // Test network calculations
    IpAddress ip("***********00");
    IpAddress netmask("*************");
    
    IpAddress network = calculate_network_address(ip, netmask);
    assert(network == IpAddress("***********"));
    
    IpAddress broadcast = calculate_broadcast_address(network, netmask);
    assert(broadcast == IpAddress("***********55"));
    
    std::cout << "  Network: " << ip.to_string() << "/" << netmask.to_string() 
              << " = " << network.to_string() << std::endl;
    std::cout << "  Broadcast: " << broadcast.to_string() << std::endl;
    
    std::cout << "IPv4 utilities tests passed!" << std::endl;
}

void test_ipv4_fragmentation() {
    std::cout << "Testing IPv4 fragmentation flags..." << std::endl;
    
    Ipv4Header header;
    
    // Test initial state
    assert(!header.is_fragmented());
    assert(!header.has_more_fragments());
    assert(!header.dont_fragment());
    assert(header.get_fragment_offset() == 0);
    
    // Test setting flags
    header.set_dont_fragment(true);
    assert(header.dont_fragment());
    
    header.set_more_fragments(true);
    assert(header.has_more_fragments());
    assert(header.is_fragmented());
    
    header.set_fragment_offset(100);
    assert(header.get_fragment_offset() == 100);
    assert(header.is_fragmented());
    
    std::cout << "IPv4 fragmentation tests passed!" << std::endl;
}

void test_ipv4_checksum() {
    std::cout << "Testing IPv4 checksum calculation..." << std::endl;
    
    IpAddress src("***********");
    IpAddress dst("***********");
    
    Ipv4Header header(src, dst, IPV4_PROTOCOL_TCP, 100);
    
    // Calculate checksum
    header.calculate_checksum();
    assert(header.header_checksum != 0);
    
    // Verify checksum
    assert(header.verify_checksum());
    
    // Corrupt checksum and verify it fails
    uint16_t original_checksum = header.header_checksum;
    header.header_checksum = original_checksum ^ 0xFFFF;
    assert(!header.verify_checksum());
    
    // Restore checksum
    header.header_checksum = original_checksum;
    assert(header.verify_checksum());
    
    std::cout << "IPv4 checksum tests passed!" << std::endl;
}

int main() {
    std::cout << "=== IPv4 Protocol Tests ===" << std::endl;
    
    try {
        test_ipv4_header();
        test_ipv4_packet();
        test_route_entry();
        test_routing_table();
        test_ipv4_utilities();
        test_ipv4_fragmentation();
        test_ipv4_checksum();
        
        std::cout << std::endl << "All IPv4 tests passed successfully!" << std::endl;
        std::cout << "IPv4 protocol implementation is working correctly." << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
