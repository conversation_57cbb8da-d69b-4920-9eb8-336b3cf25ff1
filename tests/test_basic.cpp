#include "utils.h"
#include "ethernet.h"
#include <iostream>
#include <cassert>
#include <vector>

// Simple test that doesn't require TUN/TAP or Linux-specific features
void test_basic_utilities() {
    std::cout << "Testing basic utilities..." << std::endl;
    
    // Test MAC address
    MacAddress mac1;
    assert(mac1.is_zero());
    
    MacAddress mac2("aa:bb:cc:dd:ee:ff");
    assert(mac2.to_string() == "aa:bb:cc:dd:ee:ff");
    assert(!mac2.is_zero());
    
    MacAddress broadcast("ff:ff:ff:ff:ff:ff");
    assert(broadcast.is_broadcast());
    
    std::cout << "  MAC address tests passed" << std::endl;
    
    // Test IP address
    IpAddress ip1;
    assert(ip1.to_string() == "0.0.0.0");
    
    IpAddress ip2("***********");
    assert(ip2.to_string() == "***********");
    
    IpAddress ip3(192, 168, 1, 1);
    assert(ip3 == ip2);
    
    std::cout << "  IP address tests passed" << std::endl;
    
    // Test checksum calculation
    std::string test_data = "Hello, World!";
    uint16_t checksum = calculate_checksum(test_data.c_str(), test_data.length());
    assert(checksum != 0); // Should not be zero for this data
    
    std::cout << "  Checksum calculation tests passed" << std::endl;
    
    // Test buffer
    Buffer buffer;
    assert(buffer.empty());
    
    buffer.append(test_data.c_str(), test_data.length());
    assert(buffer.size() == test_data.length());
    assert(!buffer.empty());
    
    buffer.clear();
    assert(buffer.empty());
    
    std::cout << "  Buffer tests passed" << std::endl;
}

void test_ethernet_frame_operations() {
    std::cout << "Testing Ethernet frame operations..." << std::endl;
    
    MacAddress src_mac("aa:bb:cc:dd:ee:ff");
    MacAddress dst_mac("11:22:33:44:55:66");
    
    // Test frame creation
    std::string payload = "Test payload for Ethernet frame";
    EthernetFrame frame(dst_mac, src_mac, ETHERTYPE_IPV4, 
                       reinterpret_cast<const uint8_t*>(payload.c_str()), 
                       payload.length());
    
    assert(frame.header().src_mac == src_mac);
    assert(frame.header().dst_mac == dst_mac);
    assert(frame.header().ethertype == ETHERTYPE_IPV4);
    assert(frame.payload_size() == payload.length());
    
    std::cout << "  Frame creation tests passed" << std::endl;
    
    // Test serialization/deserialization
    auto serialized = frame.serialize();
    assert(serialized.size() == ETHERNET_HEADER_SIZE + payload.length());
    
    EthernetFrame frame2;
    assert(frame2.deserialize(serialized.data(), serialized.size()));
    assert(frame2.header().src_mac == src_mac);
    assert(frame2.header().dst_mac == dst_mac);
    assert(frame2.header().ethertype == ETHERTYPE_IPV4);
    assert(frame2.payload_size() == payload.length());
    
    std::cout << "  Serialization tests passed" << std::endl;
    
    // Test frame validation
    assert(frame.is_valid());
    
    // Test broadcast detection
    EthernetFrame broadcast_frame(MacAddress("ff:ff:ff:ff:ff:ff"), src_mac, 
                                 ETHERTYPE_ARP, nullptr, 0);
    assert(broadcast_frame.is_broadcast());
    
    // Test multicast detection
    EthernetFrame multicast_frame(MacAddress("01:00:5e:00:00:01"), src_mac, 
                                 ETHERTYPE_IPV4, nullptr, 0);
    assert(multicast_frame.is_multicast());
    
    std::cout << "  Frame validation tests passed" << std::endl;
}

void test_network_byte_order() {
    std::cout << "Testing network byte order conversions..." << std::endl;
    
    uint16_t host16 = 0x1234;
    uint16_t net16 = htons_custom(host16);
    uint16_t back16 = ntohs_custom(net16);
    assert(back16 == host16);
    
    uint32_t host32 = 0x12345678;
    uint32_t net32 = htonl_custom(host32);
    uint32_t back32 = ntohl_custom(net32);
    assert(back32 == host32);
    
    std::cout << "  Byte order conversion tests passed" << std::endl;
}

void test_logging() {
    std::cout << "Testing logging functionality..." << std::endl;
    
    Logger::set_level(LogLevel::DEBUG);
    Logger::debug("This is a debug message");
    Logger::info("This is an info message");
    Logger::warning("This is a warning message");
    Logger::error("This is an error message");
    
    Logger::set_level(LogLevel::ERROR);
    Logger::debug("This debug message should not appear");
    Logger::error("This error message should appear");
    
    std::cout << "  Logging tests passed" << std::endl;
}

void test_hex_dump() {
    std::cout << "Testing hex dump functionality..." << std::endl;
    
    std::string test_data = "Hello, World! This is a test string for hex dump.";
    std::cout << "  Hex dump of test data:" << std::endl;
    hex_dump(test_data.c_str(), test_data.length(), "    ");
    
    std::cout << "  Hex dump tests passed" << std::endl;
}

void test_random_utilities() {
    std::cout << "Testing random utilities..." << std::endl;
    
    uint32_t rand1 = random_uint32();
    uint32_t rand2 = random_uint32();
    assert(rand1 != rand2); // Very unlikely to be equal
    
    uint16_t rand16_1 = random_uint16();
    uint16_t rand16_2 = random_uint16();
    assert(rand16_1 != rand16_2); // Very unlikely to be equal
    
    MacAddress random_mac = generate_random_mac();
    assert(!random_mac.is_zero());
    assert(!random_mac.is_broadcast());
    
    std::cout << "  Random MAC: " << random_mac.to_string() << std::endl;
    std::cout << "  Random utilities tests passed" << std::endl;
}

void test_string_utilities() {
    std::cout << "Testing string utilities..." << std::endl;
    
    std::string test_str = "  hello,world,test  ";
    std::string trimmed = trim(test_str);
    assert(trimmed == "hello,world,test");
    
    std::vector<std::string> tokens = split(trimmed, ',');
    assert(tokens.size() == 3);
    assert(tokens[0] == "hello");
    assert(tokens[1] == "world");
    assert(tokens[2] == "test");
    
    std::cout << "  String utilities tests passed" << std::endl;
}

int main() {
    std::cout << "=== Basic TCP/IP Stack Tests ===" << std::endl;
    std::cout << "Running tests that don't require TUN/TAP or Linux-specific features" << std::endl;
    std::cout << std::endl;
    
    try {
        test_basic_utilities();
        test_ethernet_frame_operations();
        test_network_byte_order();
        test_logging();
        test_hex_dump();
        test_random_utilities();
        test_string_utilities();
        
        std::cout << std::endl << "=== All Basic Tests Passed Successfully! ===" << std::endl;
        std::cout << "The core TCP/IP stack components are working correctly." << std::endl;
        std::cout << "Note: TUN/TAP functionality requires Linux for full testing." << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
