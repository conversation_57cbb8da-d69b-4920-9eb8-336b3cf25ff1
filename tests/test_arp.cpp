#include "arp.h"
#include "ethernet.h"
#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>

class TestEthernetInterface : public EthernetInterface {
public:
    TestEthernetInterface(const MacAddress& mac) : EthernetInterface(mac) {}
    
    bool send_frame(const EthernetFrame& frame) override {
        sent_frames_.push_back(frame);
        stats_.frames_sent++;
        stats_.bytes_sent += frame.total_size();
        
        // Simulate frame reception for testing
        if (auto_respond_) {
            simulate_frame_reception(frame);
        }
        
        return true;
    }
    
    void simulate_receive_frame(const EthernetFrame& frame) {
        handle_received_frame(frame);
    }
    
    const std::vector<EthernetFrame>& get_sent_frames() const {
        return sent_frames_;
    }
    
    void clear_sent_frames() {
        sent_frames_.clear();
    }
    
    void set_auto_respond(bool enable) {
        auto_respond_ = enable;
    }
    
private:
    std::vector<EthernetFrame> sent_frames_;
    bool auto_respond_ = false;
    
    void simulate_frame_reception(const EthernetFrame& frame) {
        // Simulate ARP response for testing
        if (frame.header().ethertype == ETHERTYPE_ARP && frame.payload_size() >= ArpHeader::size()) {
            ArpHeader arp_header = ArpHeader::deserialize(frame.payload());
            
            if (arp_header.is_request() && arp_header.target_ip == IpAddress("***********")) {
                // Simulate response from ***********
                MacAddress response_mac("bb:bb:bb:bb:bb:bb");
                ArpHeader response_header(ARP_OP_REPLY, response_mac, arp_header.target_ip,
                                        arp_header.sender_mac, arp_header.sender_ip);
                
                std::vector<uint8_t> response_data(ArpHeader::size());
                response_header.serialize(response_data.data());
                
                EthernetFrame response_frame(arp_header.sender_mac, response_mac, ETHERTYPE_ARP,
                                           response_data.data(), response_data.size());
                
                // Simulate receiving the response after a short delay
                std::thread([this, response_frame]() {
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                    handle_received_frame(response_frame);
                }).detach();
            }
        }
    }
};

void test_arp_header() {
    std::cout << "Testing ARP header..." << std::endl;
    
    MacAddress sender_mac("aa:bb:cc:dd:ee:ff");
    IpAddress sender_ip("***********");
    MacAddress target_mac("11:22:33:44:55:66");
    IpAddress target_ip("***********");
    
    // Test ARP request
    ArpHeader request(ARP_OP_REQUEST, sender_mac, sender_ip, MacAddress(), target_ip);
    assert(request.is_request());
    assert(!request.is_reply());
    assert(request.is_valid());
    assert(request.sender_mac == sender_mac);
    assert(request.sender_ip == sender_ip);
    assert(request.target_ip == target_ip);
    
    // Test serialization
    std::vector<uint8_t> buffer(ArpHeader::size());
    request.serialize(buffer.data());
    
    // Test deserialization
    ArpHeader request2 = ArpHeader::deserialize(buffer.data());
    assert(request2.operation == request.operation);
    assert(request2.sender_mac == request.sender_mac);
    assert(request2.sender_ip == request.sender_ip);
    assert(request2.target_ip == request.target_ip);
    assert(request2.is_valid());
    
    // Test ARP reply
    ArpHeader reply(ARP_OP_REPLY, target_mac, target_ip, sender_mac, sender_ip);
    assert(reply.is_reply());
    assert(!reply.is_request());
    assert(reply.is_valid());
    
    std::cout << "  Request: " << request.to_string() << std::endl;
    std::cout << "  Reply: " << reply.to_string() << std::endl;
    std::cout << "ARP header tests passed!" << std::endl;
}

void test_arp_table() {
    std::cout << "Testing ARP table..." << std::endl;
    
    ArpTable table;
    assert(table.size() == 0);
    
    IpAddress ip1("***********");
    MacAddress mac1("aa:bb:cc:dd:ee:ff");
    IpAddress ip2("***********");
    MacAddress mac2("11:22:33:44:55:66");
    
    // Test adding entries
    table.add_entry(ip1, mac1);
    table.add_entry(ip2, mac2, true); // Static entry
    assert(table.size() == 2);
    
    // Test lookup
    MacAddress found_mac;
    assert(table.lookup(ip1, found_mac));
    assert(found_mac == mac1);
    assert(table.lookup(ip2, found_mac));
    assert(found_mac == mac2);
    
    // Test has_entry
    assert(table.has_entry(ip1));
    assert(table.has_entry(ip2));
    assert(!table.has_entry(IpAddress("***********")));
    
    // Test get_all_entries
    auto entries = table.get_all_entries();
    assert(entries.size() == 2);
    
    // Test removing entry
    assert(table.remove_entry(ip1));
    assert(!table.has_entry(ip1));
    assert(table.size() == 1);
    
    // Test clear_dynamic_entries (should not remove static entry)
    table.add_entry(ip1, mac1); // Add back as dynamic
    assert(table.size() == 2);
    table.clear_dynamic_entries();
    assert(table.size() == 1);
    assert(table.has_entry(ip2)); // Static entry should remain
    
    // Test clear_all_entries
    table.clear_all_entries();
    assert(table.size() == 0);
    
    std::cout << "ARP table tests passed!" << std::endl;
}

void test_arp_protocol_basic() {
    std::cout << "Testing ARP protocol basic functionality..." << std::endl;
    
    MacAddress local_mac("aa:bb:cc:dd:ee:ff");
    IpAddress local_ip("***********");
    
    TestEthernetInterface interface(local_mac);
    ArpProtocol arp(interface, local_ip);
    
    // Set up frame handler to pass ARP frames to protocol
    interface.set_frame_handler([&arp](const EthernetFrame& frame) {
        arp.handle_ethernet_frame(frame);
    });
    
    // Test sending ARP request
    IpAddress target_ip("***********");
    assert(arp.send_request(target_ip));
    
    auto sent_frames = interface.get_sent_frames();
    assert(sent_frames.size() == 1);
    
    const auto& frame = sent_frames[0];
    assert(frame.header().ethertype == ETHERTYPE_ARP);
    assert(frame.header().dst_mac.is_broadcast());
    assert(frame.header().src_mac == local_mac);
    
    // Verify ARP request content
    ArpHeader arp_header = ArpHeader::deserialize(frame.payload());
    assert(arp_header.is_request());
    assert(arp_header.sender_mac == local_mac);
    assert(arp_header.sender_ip == local_ip);
    assert(arp_header.target_ip == target_ip);
    
    std::cout << "  Sent ARP request: " << arp_header.to_string() << std::endl;
    
    // Test statistics
    auto stats = arp.get_statistics();
    assert(stats.requests_sent == 1);
    
    std::cout << "ARP protocol basic tests passed!" << std::endl;
}

void test_arp_request_response() {
    std::cout << "Testing ARP request/response cycle..." << std::endl;
    
    MacAddress local_mac("aa:bb:cc:dd:ee:ff");
    IpAddress local_ip("***********");
    MacAddress remote_mac("11:22:33:44:55:66");
    IpAddress remote_ip("***********");
    
    TestEthernetInterface interface(local_mac);
    ArpProtocol arp(interface, local_ip);
    
    // Set up frame handler
    interface.set_frame_handler([&arp](const EthernetFrame& frame) {
        arp.handle_ethernet_frame(frame);
    });
    
    // Simulate receiving an ARP request for our IP
    ArpHeader incoming_request(ARP_OP_REQUEST, remote_mac, remote_ip, MacAddress(), local_ip);
    std::vector<uint8_t> request_data(ArpHeader::size());
    incoming_request.serialize(request_data.data());
    
    EthernetFrame request_frame(MacAddress("ff:ff:ff:ff:ff:ff"), remote_mac, ETHERTYPE_ARP,
                               request_data.data(), request_data.size());
    
    interface.clear_sent_frames();
    interface.simulate_receive_frame(request_frame);
    
    // Should have sent a reply
    auto sent_frames = interface.get_sent_frames();
    assert(sent_frames.size() == 1);
    
    const auto& reply_frame = sent_frames[0];
    assert(reply_frame.header().ethertype == ETHERTYPE_ARP);
    assert(reply_frame.header().dst_mac == remote_mac);
    assert(reply_frame.header().src_mac == local_mac);
    
    // Verify ARP reply content
    ArpHeader reply_header = ArpHeader::deserialize(reply_frame.payload());
    assert(reply_header.is_reply());
    assert(reply_header.sender_mac == local_mac);
    assert(reply_header.sender_ip == local_ip);
    assert(reply_header.target_mac == remote_mac);
    assert(reply_header.target_ip == remote_ip);
    
    std::cout << "  Received request: " << incoming_request.to_string() << std::endl;
    std::cout << "  Sent reply: " << reply_header.to_string() << std::endl;
    
    // Check that remote entry was added to ARP table
    MacAddress found_mac;
    assert(arp.get_table().lookup(remote_ip, found_mac));
    assert(found_mac == remote_mac);
    
    // Test statistics
    auto stats = arp.get_statistics();
    assert(stats.requests_received == 1);
    assert(stats.replies_sent == 1);
    
    std::cout << "ARP request/response tests passed!" << std::endl;
}

void test_arp_address_resolution() {
    std::cout << "Testing ARP address resolution..." << std::endl;
    
    MacAddress local_mac("aa:bb:cc:dd:ee:ff");
    IpAddress local_ip("***********");
    IpAddress target_ip("***********");
    
    TestEthernetInterface interface(local_mac);
    ArpProtocol arp(interface, local_ip);
    
    // Set up frame handler
    interface.set_frame_handler([&arp](const EthernetFrame& frame) {
        arp.handle_ethernet_frame(frame);
    });
    
    // Enable auto-response for testing
    interface.set_auto_respond(true);
    
    // Test address resolution
    MacAddress resolved_mac;
    bool resolved = arp.resolve_address(target_ip, resolved_mac, std::chrono::milliseconds(100));
    
    assert(resolved);
    assert(resolved_mac == MacAddress("bb:bb:bb:bb:bb:bb")); // From auto-response
    
    // Verify entry is in ARP table
    MacAddress table_mac;
    assert(arp.get_table().lookup(target_ip, table_mac));
    assert(table_mac == resolved_mac);
    
    std::cout << "  Resolved " << target_ip.to_string() 
              << " -> " << resolved_mac.to_string() << std::endl;
    
    // Test cache hit
    MacAddress cached_mac;
    auto start_time = std::chrono::steady_clock::now();
    bool cached = arp.resolve_address(target_ip, cached_mac, std::chrono::milliseconds(100));
    auto end_time = std::chrono::steady_clock::now();
    
    assert(cached);
    assert(cached_mac == resolved_mac);
    
    // Should be much faster (cache hit)
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    assert(duration.count() < 10); // Should be very fast
    
    // Test statistics
    auto stats = arp.get_statistics();
    assert(stats.cache_hits >= 1);
    assert(stats.cache_misses >= 1);
    
    std::cout << "ARP address resolution tests passed!" << std::endl;
}

void test_arp_table_expiration() {
    std::cout << "Testing ARP table expiration..." << std::endl;
    
    ArpTable table;
    IpAddress ip("***********");
    MacAddress mac("aa:bb:cc:dd:ee:ff");
    
    // Add dynamic entry
    table.add_entry(ip, mac);
    assert(table.has_entry(ip));
    
    // Should not be expired immediately
    table.cleanup_expired_entries(std::chrono::seconds(300));
    assert(table.has_entry(ip));
    
    // Should be expired with very short timeout
    table.cleanup_expired_entries(std::chrono::seconds(0));
    assert(!table.has_entry(ip));
    
    // Add static entry
    table.add_entry(ip, mac, true);
    assert(table.has_entry(ip));
    
    // Static entry should not expire
    table.cleanup_expired_entries(std::chrono::seconds(0));
    assert(table.has_entry(ip));
    
    std::cout << "ARP table expiration tests passed!" << std::endl;
}

void test_arp_utilities() {
    std::cout << "Testing ARP utilities..." << std::endl;
    
    assert(arp_operation_to_string(ARP_OP_REQUEST) == "Request");
    assert(arp_operation_to_string(ARP_OP_REPLY) == "Reply");
    assert(arp_operation_to_string(999) == "Unknown");
    
    // Test packet validation
    ArpHeader valid_header(ARP_OP_REQUEST, MacAddress("aa:bb:cc:dd:ee:ff"), 
                          IpAddress("***********"), MacAddress(), IpAddress("***********"));
    std::vector<uint8_t> valid_data(ArpHeader::size());
    valid_header.serialize(valid_data.data());
    
    assert(is_valid_arp_packet(valid_data.data(), valid_data.size()));
    assert(!is_valid_arp_packet(valid_data.data(), 10)); // Too small
    
    std::cout << "ARP utilities tests passed!" << std::endl;
}

int main() {
    std::cout << "=== ARP Protocol Tests ===" << std::endl;
    
    try {
        test_arp_header();
        test_arp_table();
        test_arp_protocol_basic();
        test_arp_request_response();
        test_arp_address_resolution();
        test_arp_table_expiration();
        test_arp_utilities();
        
        std::cout << std::endl << "All ARP tests passed successfully!" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
