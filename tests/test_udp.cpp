#include "udp.h"
#include <iostream>
#include <cassert>
#include <cstring>
#include <thread>
#include <chrono>

void test_udp_header() {
    std::cout << "Testing UDP header..." << std::endl;
    
    // Test default constructor
    UdpHeader header1;
    assert(header1.source_port == 0);
    assert(header1.destination_port == 0);
    assert(header1.length == 0);
    assert(header1.checksum == 0);
    
    // Test parameterized constructor
    UdpHeader header2(12345, 80, 100);
    assert(header2.source_port == 12345);
    assert(header2.destination_port == 80);
    assert(header2.length == UDP_HEADER_SIZE + 100);
    assert(header2.get_payload_length() == 100);
    
    // Test serialization/deserialization
    std::vector<uint8_t> buffer(UDP_HEADER_SIZE);
    header2.serialize(buffer.data());
    
    UdpHeader header3 = UdpHeader::deserialize(buffer.data());
    assert(header3.source_port == header2.source_port);
    assert(header3.destination_port == header2.destination_port);
    assert(header3.length == header2.length);
    
    // Test validity
    assert(header2.is_valid());
    assert(header3.is_valid());
    
    std::cout << "  " << header2.to_string() << std::endl;
    std::cout << "UDP header tests passed!" << std::endl;
}

void test_udp_checksum() {
    std::cout << "Testing UDP checksum..." << std::endl;
    
    IpAddress src_ip("***********");
    IpAddress dst_ip("***********");
    
    UdpHeader header(12345, 80, 10);
    std::string payload = "Hello UDP!";
    
    // Calculate checksum
    header.calculate_checksum(src_ip, dst_ip, 
                             reinterpret_cast<const uint8_t*>(payload.c_str()), 
                             payload.length());
    
    std::cout << "  Checksum: 0x" << std::hex << header.checksum << std::dec << std::endl;
    
    // Verify checksum
    bool valid = header.verify_checksum(src_ip, dst_ip,
                                       reinterpret_cast<const uint8_t*>(payload.c_str()),
                                       payload.length());
    assert(valid);
    
    std::cout << "UDP checksum tests passed!" << std::endl;
}

void test_udp_packet() {
    std::cout << "Testing UDP packet..." << std::endl;
    
    std::string payload_str = "Hello, UDP World!";
    const uint8_t* payload_data = reinterpret_cast<const uint8_t*>(payload_str.c_str());
    size_t payload_size = payload_str.length();
    
    // Test packet creation
    UdpPacket packet1(12345, 80, payload_data, payload_size);
    assert(packet1.header().source_port == 12345);
    assert(packet1.header().destination_port == 80);
    assert(packet1.payload_size() == payload_size);
    assert(std::memcmp(packet1.payload(), payload_data, payload_size) == 0);
    
    // Test serialization
    IpAddress src_ip("********");
    IpAddress dst_ip("********");
    auto serialized = packet1.serialize(src_ip, dst_ip);
    assert(serialized.size() == UDP_HEADER_SIZE + payload_size);
    
    // Test deserialization
    UdpPacket packet2;
    assert(packet2.deserialize(serialized.data(), serialized.size()));
    assert(packet2.header().source_port == 12345);
    assert(packet2.header().destination_port == 80);
    assert(packet2.payload_size() == payload_size);
    
    // Verify payload content
    assert(std::memcmp(packet2.payload(), payload_data, payload_size) == 0);
    
    // Test validity
    assert(packet2.is_valid(src_ip, dst_ip));
    
    std::cout << "  " << packet1.to_string() << std::endl;
    std::cout << "UDP packet tests passed!" << std::endl;
}

void test_udp_socket_basic() {
    std::cout << "Testing UDP socket basic..." << std::endl;
    
    // Test default constructor
    UdpSocket socket1;
    assert(!socket1.is_bound());
    assert(socket1.get_local_port() == 0);
    
    // Test binding
    assert(socket1.bind(12345));
    assert(socket1.is_bound());
    assert(socket1.get_local_port() == 12345);
    
    // Test double binding (should fail)
    assert(!socket1.bind(12346));
    
    // Test constructor with port
    UdpSocket socket2(54321);
    assert(socket2.is_bound());
    assert(socket2.get_local_port() == 54321);
    
    // Test sending
    std::string data = "test message";
    IpAddress dest_ip("*************");
    bool sent = socket1.send_to(dest_ip, 80, 
                               reinterpret_cast<const uint8_t*>(data.c_str()), 
                               data.length());
    assert(sent);
    
    // Check statistics
    auto stats = socket1.get_statistics();
    assert(stats.packets_sent == 1);
    assert(stats.bytes_sent == data.length());
    
    std::cout << "UDP socket basic tests passed!" << std::endl;
}

void test_udp_socket_communication() {
    std::cout << "Testing UDP socket communication..." << std::endl;
    
    // Create two sockets
    UdpSocket sender(12345);
    UdpSocket receiver(54321);
    
    assert(sender.is_bound());
    assert(receiver.is_bound());
    
    // Prepare test data
    std::string message = "Hello from sender!";
    IpAddress sender_ip("***********");
    IpAddress receiver_ip("***********");
    
    // Create UDP packet manually and simulate reception
    UdpPacket packet(sender.get_local_port(), receiver.get_local_port(),
                    reinterpret_cast<const uint8_t*>(message.c_str()), message.length());
    
    // Simulate packet reception
    receiver.handle_received_packet(sender_ip, packet);
    
    // Try to receive
    IpAddress src_ip;
    uint16_t src_port;
    std::vector<uint8_t> received_data;
    
    bool received = receiver.receive_from(src_ip, src_port, received_data, 
                                         std::chrono::milliseconds(100));
    
    assert(received);
    assert(src_ip == sender_ip);
    assert(src_port == sender.get_local_port());
    assert(received_data.size() == message.length());
    assert(std::memcmp(received_data.data(), message.c_str(), message.length()) == 0);
    
    // Check statistics
    auto receiver_stats = receiver.get_statistics();
    assert(receiver_stats.packets_received == 1);
    assert(receiver_stats.bytes_received == message.length());
    
    std::cout << "  Sent: \"" << message << "\"" << std::endl;
    std::cout << "  Received from " << src_ip.to_string() << ":" << src_port << std::endl;
    std::cout << "UDP socket communication tests passed!" << std::endl;
}

void test_udp_socket_timeout() {
    std::cout << "Testing UDP socket timeout..." << std::endl;
    
    UdpSocket socket(12345);
    
    IpAddress src_ip;
    uint16_t src_port;
    std::vector<uint8_t> data;
    
    // Test timeout (should return false)
    auto start_time = std::chrono::steady_clock::now();
    bool received = socket.receive_from(src_ip, src_port, data, std::chrono::milliseconds(50));
    auto end_time = std::chrono::steady_clock::now();
    
    assert(!received);
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    assert(duration.count() >= 45); // Should be close to 50ms
    assert(duration.count() <= 100); // But not too much longer
    
    std::cout << "  Timeout test completed in " << duration.count() << "ms" << std::endl;
    std::cout << "UDP socket timeout tests passed!" << std::endl;
}

void test_udp_protocol_basic() {
    std::cout << "Testing UDP protocol basic..." << std::endl;
    
    // Create mock IPv4 protocol (we'll simulate this)
    class MockIpv4Protocol {
    public:
        // Mock implementation - not used in this test
    };
    
    MockIpv4Protocol mock_ipv4;
    // Note: We can't actually test UdpProtocol without a real Ipv4Protocol
    // So we'll test the components we can
    
    // Test port allocation logic
    std::cout << "  Testing port utilities..." << std::endl;
    
    // Test well-known port names
    assert(udp_port_to_string(53) == "DNS");
    assert(udp_port_to_string(80) == "Port 80");
    assert(udp_port_to_string(123) == "NTP");
    
    // Test packet validation
    UdpPacket valid_packet(12345, 80, nullptr, 0);
    IpAddress src("*******"), dst("*******");
    auto serialized = valid_packet.serialize(src, dst);
    
    assert(is_valid_udp_packet(serialized.data(), serialized.size()));
    assert(!is_valid_udp_packet(serialized.data(), 3)); // Too small
    
    std::cout << "UDP protocol basic tests passed!" << std::endl;
}

void test_udp_edge_cases() {
    std::cout << "Testing UDP edge cases..." << std::endl;
    
    // Test empty payload
    UdpPacket empty_packet(12345, 80, nullptr, 0);
    assert(empty_packet.payload_size() == 0);
    assert(empty_packet.header().get_payload_length() == 0);
    
    IpAddress src("*******"), dst("*******");
    auto serialized = empty_packet.serialize(src, dst);
    assert(serialized.size() == UDP_HEADER_SIZE);
    
    UdpPacket empty_packet2;
    assert(empty_packet2.deserialize(serialized.data(), serialized.size()));
    assert(empty_packet2.is_valid(src, dst));
    
    // Test maximum payload
    std::vector<uint8_t> max_payload(UDP_MAX_PACKET_SIZE - UDP_HEADER_SIZE, 0xAA);
    UdpPacket max_packet(12345, 80, max_payload.data(), max_payload.size());
    assert(max_packet.payload_size() == max_payload.size());
    
    // Test invalid header
    UdpHeader invalid_header(12345, 80, 0);
    invalid_header.length = 3; // Too small
    assert(!invalid_header.is_valid());
    
    std::cout << "UDP edge cases tests passed!" << std::endl;
}

void test_udp_statistics() {
    std::cout << "Testing UDP statistics..." << std::endl;
    
    UdpSocket socket(12345);
    
    // Initial statistics should be zero
    auto stats = socket.get_statistics();
    assert(stats.packets_sent == 0);
    assert(stats.packets_received == 0);
    assert(stats.bytes_sent == 0);
    assert(stats.bytes_received == 0);
    assert(stats.packets_dropped == 0);
    
    // Send some data
    std::string data1 = "message1";
    std::string data2 = "message2";
    IpAddress dest("*************");
    
    socket.send_to(dest, 80, reinterpret_cast<const uint8_t*>(data1.c_str()), data1.length());
    socket.send_to(dest, 80, reinterpret_cast<const uint8_t*>(data2.c_str()), data2.length());
    
    stats = socket.get_statistics();
    assert(stats.packets_sent == 2);
    assert(stats.bytes_sent == data1.length() + data2.length());
    
    // Reset statistics
    socket.reset_statistics();
    stats = socket.get_statistics();
    assert(stats.packets_sent == 0);
    assert(stats.bytes_sent == 0);
    
    std::cout << "UDP statistics tests passed!" << std::endl;
}

int main() {
    std::cout << "=== UDP Protocol Tests ===" << std::endl;
    
    try {
        test_udp_header();
        test_udp_checksum();
        test_udp_packet();
        test_udp_socket_basic();
        test_udp_socket_communication();
        test_udp_socket_timeout();
        test_udp_protocol_basic();
        test_udp_edge_cases();
        test_udp_statistics();
        
        std::cout << std::endl << "All UDP tests passed successfully!" << std::endl;
        std::cout << "UDP protocol implementation is working correctly." << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
