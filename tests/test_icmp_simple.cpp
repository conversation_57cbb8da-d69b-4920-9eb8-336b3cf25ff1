#include "icmp.h"
#include <iostream>
#include <cassert>
#include <cstring>

void test_icmp_header_basic() {
    std::cout << "Testing ICMP header basic..." << std::endl;
    
    // Test default constructor
    IcmpHeader header1;
    assert(header1.type == 0);
    assert(header1.code == 0);
    assert(header1.checksum == 0);
    assert(header1.rest == 0);

    // Test parameterized constructor
    IcmpHeader header2(ICMP_TYPE_ECHO_REQUEST, 0, 0x12345678);
    assert(header2.type == ICMP_TYPE_ECHO_REQUEST);
    assert(header2.code == 0);
    assert(header2.rest == 0x12345678);

    // Test serialization
    std::vector<uint8_t> buffer(IcmpHeader::size());
    header2.serialize(buffer.data());

    IcmpHeader header3 = IcmpHeader::deserialize(buffer.data());
    assert(header3.type == header2.type);
    assert(header3.code == header2.code);
    assert(header3.rest == header2.rest);
    
    std::cout << "  " << header2.to_string() << std::endl;
    std::cout << "ICMP header basic tests passed!" << std::endl;
}

void test_icmp_echo_header() {
    std::cout << "Testing ICMP echo header..." << std::endl;
    
    // Test default constructor
    IcmpEchoHeader echo1;
    assert(echo1.type == 0);
    assert(echo1.code == 0);
    assert(echo1.checksum == 0);
    assert(echo1.identifier == 0);
    assert(echo1.sequence == 0);
    
    // Test parameterized constructor
    IcmpEchoHeader echo2(ICMP_TYPE_ECHO_REQUEST, 0x1234, 0x5678);
    assert(echo2.type == ICMP_TYPE_ECHO_REQUEST);
    assert(echo2.identifier == 0x1234);
    assert(echo2.sequence == 0x5678);
    
    // Test serialization
    std::vector<uint8_t> buffer(IcmpEchoHeader::size());
    echo2.serialize(buffer.data());
    
    IcmpEchoHeader echo3 = IcmpEchoHeader::deserialize(buffer.data());
    assert(echo3.type == echo2.type);
    assert(echo3.identifier == echo2.identifier);
    assert(echo3.sequence == echo2.sequence);
    
    std::cout << "  " << echo2.to_string() << std::endl;
    std::cout << "ICMP echo header tests passed!" << std::endl;
}

void test_icmp_message_basic() {
    std::cout << "Testing ICMP message basic..." << std::endl;
    
    // Test default constructor
    IcmpMessage msg1;
    assert(msg1.get_type() == 0);
    assert(msg1.get_code() == 0);
    assert(msg1.payload_size() == 0);
    
    // Test with payload
    std::string payload_str = "Hello ICMP!";
    const uint8_t* payload_data = reinterpret_cast<const uint8_t*>(payload_str.c_str());
    size_t payload_size = payload_str.length();
    
    IcmpMessage msg2(ICMP_TYPE_ECHO_REQUEST, 0, payload_data, payload_size);
    assert(msg2.get_type() == ICMP_TYPE_ECHO_REQUEST);
    assert(msg2.get_code() == 0);
    assert(msg2.payload_size() == payload_size);
    assert(std::memcmp(msg2.payload(), payload_data, payload_size) == 0);
    
    // Test type checks
    assert(msg2.is_echo_request());
    assert(!msg2.is_echo_reply());
    assert(!msg2.is_dest_unreachable());
    
    std::cout << "  " << msg2.to_string() << std::endl;
    std::cout << "ICMP message basic tests passed!" << std::endl;
}

void test_icmp_serialization() {
    std::cout << "Testing ICMP serialization..." << std::endl;
    
    // Create ICMP message with echo data
    IcmpMessage msg1(ICMP_TYPE_ECHO_REQUEST, 0);
    msg1.set_rest_of_header((0x1234 << 16) | 0x5678); // identifier and sequence
    
    std::string data = "ping test";
    msg1.set_payload(reinterpret_cast<const uint8_t*>(data.c_str()), data.length());
    
    // Test serialization
    auto serialized = msg1.serialize();
    assert(serialized.size() == IcmpHeader::size() + data.length());
    
    // Test deserialization
    IcmpMessage msg2;
    assert(msg2.deserialize(serialized.data(), serialized.size()));
    assert(msg2.get_type() == msg1.get_type());
    assert(msg2.get_code() == msg1.get_code());
    assert(msg2.payload_size() == data.length());
    assert(std::memcmp(msg2.payload(), data.c_str(), data.length()) == 0);
    
    std::cout << "  Original: " << msg1.to_string() << std::endl;
    std::cout << "  Deserialized: " << msg2.to_string() << std::endl;
    std::cout << "ICMP serialization tests passed!" << std::endl;
}

void test_icmp_checksum() {
    std::cout << "Testing ICMP checksum..." << std::endl;
    
    // Create a simple ICMP message
    std::string data = "test";
    IcmpMessage msg(ICMP_TYPE_ECHO_REQUEST, 0, 
                   reinterpret_cast<const uint8_t*>(data.c_str()), data.length());
    msg.set_rest_of_header((1 << 16) | 1); // id=1, seq=1
    
    // Serialize (this calculates checksum)
    auto serialized = msg.serialize();
    
    // Deserialize and verify
    IcmpMessage msg2;
    assert(msg2.deserialize(serialized.data(), serialized.size()));
    assert(msg2.is_valid());
    
    std::cout << "  Checksum: 0x" << std::hex << msg2.header().checksum << std::dec << std::endl;
    std::cout << "ICMP checksum tests passed!" << std::endl;
}

void test_icmp_echo_message() {
    std::cout << "Testing ICMP echo message..." << std::endl;
    
    // Create echo request
    IcmpEchoMessage echo_req(ICMP_TYPE_ECHO_REQUEST, 0x1234, 0x5678);
    std::string data = "ping data";
    echo_req.set_payload(reinterpret_cast<const uint8_t*>(data.c_str()), data.length());
    
    assert(echo_req.get_type() == ICMP_TYPE_ECHO_REQUEST);
    assert(echo_req.get_identifier() == 0x1234);
    assert(echo_req.get_sequence() == 0x5678);
    
    // Test serialization
    auto serialized = echo_req.serialize();
    
    // Test deserialization
    IcmpEchoMessage echo_req2;
    assert(echo_req2.deserialize(serialized.data(), serialized.size()));
    assert(echo_req2.is_valid());
    assert(echo_req2.get_identifier() == 0x1234);
    assert(echo_req2.get_sequence() == 0x5678);
    
    // Create echo reply
    IcmpEchoMessage echo_reply = echo_req.create_reply();
    assert(echo_reply.get_type() == ICMP_TYPE_ECHO_REPLY);
    assert(echo_reply.get_identifier() == 0x1234);
    assert(echo_reply.get_sequence() == 0x5678);
    
    std::cout << "  Request: " << echo_req.to_string() << std::endl;
    std::cout << "  Reply: " << echo_reply.to_string() << std::endl;
    std::cout << "ICMP echo message tests passed!" << std::endl;
}

void test_icmp_utilities() {
    std::cout << "Testing ICMP utilities..." << std::endl;
    
    // Test type strings
    assert(icmp_type_to_string(ICMP_TYPE_ECHO_REQUEST) == "Echo Request");
    assert(icmp_type_to_string(ICMP_TYPE_ECHO_REPLY) == "Echo Reply");
    assert(icmp_type_to_string(ICMP_TYPE_DEST_UNREACHABLE) == "Destination Unreachable");
    assert(icmp_type_to_string(255) == "Type 255");
    
    // Test code strings
    assert(icmp_code_to_string(ICMP_TYPE_DEST_UNREACHABLE, ICMP_CODE_HOST_UNREACHABLE) == "Host Unreachable");
    assert(icmp_code_to_string(ICMP_TYPE_TIME_EXCEEDED, ICMP_CODE_TTL_EXCEEDED) == "TTL Exceeded");
    
    // Test message validation
    IcmpMessage valid_msg(ICMP_TYPE_ECHO_REQUEST, 0);
    auto valid_data = valid_msg.serialize();
    assert(is_valid_icmp_message(valid_data.data(), valid_data.size()));
    assert(!is_valid_icmp_message(valid_data.data(), 3)); // Too small
    
    std::cout << "ICMP utilities tests passed!" << std::endl;
}

int main() {
    std::cout << "=== Simple ICMP Protocol Tests ===" << std::endl;
    std::cout << "Testing ICMP implementation step by step" << std::endl;
    std::cout << std::endl;
    
    try {
        test_icmp_header_basic();
        test_icmp_echo_header();
        test_icmp_message_basic();
        test_icmp_serialization();
        test_icmp_checksum();
        test_icmp_echo_message();
        test_icmp_utilities();
        
        std::cout << std::endl << "All simple ICMP tests passed successfully!" << std::endl;
        std::cout << "ICMP protocol implementation is working correctly." << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
