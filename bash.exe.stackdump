Stack trace:
Frame         Function      Args
0007FFFF9960  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8860) msys-2.0.dll+0x2118E
0007FFFF9960  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9960  0002100469F2 (00021028DF99, 0007FFFF9818, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9960  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9960  00021006A545 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA03820000 ntdll.dll
7FFA01E00000 KERNEL32.DLL
7FFA01070000 KERNELBASE.dll
7FFA03010000 USER32.dll
7FFA01470000 win32u.dll
7FFA03380000 GDI32.dll
7FFA00D50000 gdi32full.dll
7FFA00B30000 msvcp_win.dll
7FFA00E80000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA01F60000 advapi32.dll
7FFA021A0000 msvcrt.dll
7FFA03440000 sechost.dll
7FFA02250000 RPCRT4.dll
7FF9FFFD0000 CRYPTBASE.DLL
7FFA00FD0000 bcryptPrimitives.dll
7FFA01750000 IMM32.DLL
