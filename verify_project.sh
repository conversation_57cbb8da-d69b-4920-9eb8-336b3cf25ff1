#!/bin/bash

# TCP/IP Stack Project Verification Script
# This script performs comprehensive end-to-end testing

echo "=========================================="
echo "    TCP/IP Stack Project Verification"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_TOTAL=0

# Function to run test and check result
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "\n${YELLOW}Testing: $test_name${NC}"
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
    fi
}

# Function to check file exists
check_file() {
    local file_path="$1"
    local description="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    if [ -f "$file_path" ]; then
        echo -e "${GREEN}✅ EXISTS: $description${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}❌ MISSING: $description${NC}"
    fi
}

echo -e "\n=== 1. PROJECT STRUCTURE VERIFICATION ==="

# Check essential files
check_file "README.md" "Project documentation"
check_file "PROJECT_SUMMARY.md" "Project summary"
check_file "Makefile" "Build system"
check_file "bin/tcp_stack" "Main application"
check_file "bin/libtcpstack.a" "Static library"

# Check header files
echo -e "\n--- Header Files ---"
check_file "include/ethernet.h" "Ethernet header"
check_file "include/arp.h" "ARP header"
check_file "include/ipv4.h" "IPv4 header"
check_file "include/icmp.h" "ICMP header"
check_file "include/udp.h" "UDP header"
check_file "include/tcp.h" "TCP header"
check_file "include/utils.h" "Utilities header"

# Check source files
echo -e "\n--- Source Files ---"
check_file "src/ethernet.cpp" "Ethernet implementation"
check_file "src/arp.cpp" "ARP implementation"
check_file "src/ipv4.cpp" "IPv4 implementation"
check_file "src/icmp.cpp" "ICMP implementation"
check_file "src/udp.cpp" "UDP implementation"
check_file "src/tcp.cpp" "TCP implementation"
check_file "src/utils.cpp" "Utilities implementation"

# Check test files
echo -e "\n--- Test Binaries ---"
check_file "bin/test_basic" "Basic tests"
check_file "bin/test_arp_simple" "ARP tests"
check_file "bin/test_ipv4_simple" "IPv4 tests"
check_file "bin/test_icmp_simple" "ICMP tests"
check_file "bin/test_udp_simple" "UDP tests"
check_file "bin/test_tcp_simple" "TCP tests"

# Check demo applications
echo -e "\n--- Demo Applications ---"
check_file "bin/ping_responder" "ICMP ping responder"
check_file "bin/udp_echo_server" "UDP echo server"
check_file "bin/tcp_echo_server" "TCP echo server"
check_file "bin/http_client" "HTTP client"

echo -e "\n=== 2. FUNCTIONAL TESTING ==="

# Run protocol tests
run_test "Basic utilities and Ethernet" "./bin/test_basic"
run_test "ARP protocol functionality" "./bin/test_arp_simple"
run_test "IPv4 protocol functionality" "./bin/test_ipv4_simple"
run_test "ICMP protocol functionality" "./bin/test_icmp_simple"
run_test "UDP protocol functionality" "./bin/test_udp_simple"
run_test "TCP protocol functionality" "./bin/test_tcp_simple"

echo -e "\n=== 3. DEMO APPLICATION TESTING ==="

# Run demo applications
run_test "Main TCP/IP stack demo" "./bin/tcp_stack --demo"
run_test "ICMP ping responder demo" "./bin/ping_responder"
run_test "UDP echo server demo" "./bin/udp_echo_server"
run_test "TCP echo server demo" "./bin/tcp_echo_server"
run_test "HTTP client demo" "./bin/http_client"

echo -e "\n=== 4. BUILD SYSTEM VERIFICATION ==="

# Test clean and rebuild
run_test "Clean build system" "make clean"
run_test "Rebuild project" "make bin/tcp_stack bin/test_basic demos"

echo -e "\n=========================================="
echo -e "    VERIFICATION RESULTS"
echo -e "=========================================="

if [ $TESTS_PASSED -eq $TESTS_TOTAL ]; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED! ($TESTS_PASSED/$TESTS_TOTAL)${NC}"
    echo -e "${GREEN}✅ TCP/IP Stack implementation is COMPLETE and FUNCTIONAL${NC}"
    echo -e "${GREEN}✅ All 9 milestones successfully implemented${NC}"
    echo -e "${GREEN}✅ All protocols working correctly${NC}"
    echo -e "${GREEN}✅ All demo applications functional${NC}"
    echo -e "${GREEN}✅ Project ready for deployment${NC}"
    exit 0
else
    echo -e "${RED}❌ SOME TESTS FAILED ($TESTS_PASSED/$TESTS_TOTAL passed)${NC}"
    echo -e "${YELLOW}⚠️  Please review failed tests above${NC}"
    exit 1
fi
