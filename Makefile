# TCP/IP Stack Makefile

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g
INCLUDES = -Iinclude
LIBS = -lpthread

# Directories
SRCDIR = src
INCDIR = include
TESTDIR = tests
OBJDIR = obj
BINDIR = bin

# Source files
SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)
MAIN_OBJ = $(OBJDIR)/main.o
LIB_OBJECTS = $(filter-out $(MAIN_OBJ), $(OBJECTS))

# Test files
TEST_SOURCES = $(wildcard $(TESTDIR)/*.cpp)
TEST_OBJECTS = $(TEST_SOURCES:$(TESTDIR)/%.cpp=$(OBJDIR)/%.o)
TEST_BINARIES = $(TEST_SOURCES:$(TESTDIR)/%.cpp=$(BINDIR)/%)

# Main target
TARGET = $(BINDIR)/tcp_stack

# Library target
LIBRARY = $(BINDIR)/libtcpstack.a

# Create directories
$(shell mkdir -p $(OBJDIR) $(BINDIR))

# Default target
all: $(TARGET) $(LIBRARY) tests

# Main executable
$(TARGET): $(OBJECTS) | $(BINDIR)
	$(CXX) $(OBJECTS) -o $@ $(LIBS)

# Static library
$(LIBRARY): $(LIB_OBJECTS) | $(BINDIR)
	ar rcs $@ $(LIB_OBJECTS)

# Compile source files
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Compile test files
$(OBJDIR)/%.o: $(TESTDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Test binaries
$(BINDIR)/%: $(OBJDIR)/%.o $(LIBRARY) | $(BINDIR)
	$(CXX) $< $(LIBRARY) -o $@ $(LIBS)

# Individual test targets
test_ethernet: $(BINDIR)/test_ethernet
test_arp: $(BINDIR)/test_arp
test_ipv4: $(BINDIR)/test_ipv4
test_icmp: $(BINDIR)/test_icmp
test_udp: $(BINDIR)/test_udp
test_tcp: $(BINDIR)/test_tcp

# Demo targets
ping_demo: $(BINDIR)/ping_demo
udp_echo_demo: $(BINDIR)/udp_echo_demo
tcp_echo_demo: $(BINDIR)/tcp_echo_demo
http_get_demo: $(BINDIR)/http_get_demo

# All tests
tests: test_ethernet test_arp test_ipv4 test_icmp test_udp test_tcp

# Demo applications
$(BINDIR)/ping_responder: demos/ping_responder.cpp $(LIB_OBJECTS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(LIB_OBJECTS) $(LIBS)

$(BINDIR)/udp_echo_server: demos/udp_echo_server.cpp $(LIB_OBJECTS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(LIB_OBJECTS) $(LIBS)

$(BINDIR)/tcp_echo_server: demos/tcp_echo_server.cpp $(LIB_OBJECTS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(LIB_OBJECTS) $(LIBS)

$(BINDIR)/http_client: demos/http_client.cpp $(LIB_OBJECTS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(LIB_OBJECTS) $(LIBS)

# All demos
demos: $(BINDIR)/ping_responder $(BINDIR)/udp_echo_server $(BINDIR)/tcp_echo_server $(BINDIR)/http_client

# Run tests
run_tests: tests
	@echo "Running Ethernet tests..."
	sudo $(BINDIR)/test_ethernet
	@echo "Running ARP tests..."
	sudo $(BINDIR)/test_arp
	@echo "Running IPv4 tests..."
	sudo $(BINDIR)/test_ipv4
	@echo "Running ICMP tests..."
	sudo $(BINDIR)/test_icmp
	@echo "Running UDP tests..."
	sudo $(BINDIR)/test_udp
	@echo "Running TCP tests..."
	sudo $(BINDIR)/test_tcp

# Setup TUN/TAP interface
setup_tun:
	@echo "Setting up TUN interface..."
	sudo ip tuntap add dev tun0 mode tun
	sudo ip addr add ********/24 dev tun0
	sudo ip link set dev tun0 up
	@echo "TUN interface tun0 created with IP ********/24"

# Cleanup TUN/TAP interface
cleanup_tun:
	@echo "Cleaning up TUN interface..."
	sudo ip link set dev tun0 down 2>/dev/null || true
	sudo ip tuntap del dev tun0 mode tun 2>/dev/null || true
	@echo "TUN interface cleaned up"

# Install dependencies (Ubuntu/Debian)
install_deps:
	@echo "Installing dependencies..."
	sudo apt-get update
	sudo apt-get install -y build-essential g++ make iproute2 net-tools tcpdump wireshark

# Clean build artifacts
clean:
	rm -rf $(OBJDIR) $(BINDIR)

# Clean and rebuild
rebuild: clean all

# Format code (requires clang-format)
format:
	find $(SRCDIR) $(INCDIR) $(TESTDIR) -name "*.cpp" -o -name "*.h" | xargs clang-format -i

# Static analysis (requires cppcheck)
analyze:
	cppcheck --enable=all --std=c++17 $(SRCDIR) $(INCDIR)

# Generate documentation (requires doxygen)
docs:
	doxygen Doxyfile

# Package for distribution
package: all
	tar -czf tcp_stack.tar.gz $(BINDIR) $(INCDIR) $(SRCDIR) Makefile README.md

# Help target
help:
	@echo "TCP/IP Stack Build System"
	@echo ""
	@echo "Targets:"
	@echo "  all           - Build main executable, library, and tests"
	@echo "  $(TARGET)     - Build main executable"
	@echo "  $(LIBRARY)    - Build static library"
	@echo "  tests         - Build all test programs"
	@echo "  demos         - Build all demo programs"
	@echo "  run_tests     - Build and run all tests (requires sudo)"
	@echo ""
	@echo "Individual test targets:"
	@echo "  test_ethernet - Build Ethernet test"
	@echo "  test_arp      - Build ARP test"
	@echo "  test_ipv4     - Build IPv4 test"
	@echo "  test_icmp     - Build ICMP test"
	@echo "  test_udp      - Build UDP test"
	@echo "  test_tcp      - Build TCP test"
	@echo ""
	@echo "Demo targets:"
	@echo "  ping_demo     - Build ping demo"
	@echo "  udp_echo_demo - Build UDP echo demo"
	@echo "  tcp_echo_demo - Build TCP echo demo"
	@echo "  http_get_demo - Build HTTP GET demo"
	@echo ""
	@echo "Utility targets:"
	@echo "  setup_tun     - Setup TUN interface (requires sudo)"
	@echo "  cleanup_tun   - Cleanup TUN interface (requires sudo)"
	@echo "  install_deps  - Install build dependencies"
	@echo "  clean         - Remove build artifacts"
	@echo "  rebuild       - Clean and rebuild"
	@echo "  format        - Format source code"
	@echo "  analyze       - Run static analysis"
	@echo "  docs          - Generate documentation"
	@echo "  package       - Create distribution package"
	@echo "  help          - Show this help"

# Create directories
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

# Phony targets
.PHONY: all tests demos run_tests setup_tun cleanup_tun install_deps clean rebuild format analyze docs package help

# Dependencies
-include $(OBJECTS:.o=.d)
-include $(TEST_OBJECTS:.o=.d)

# Generate dependency files
$(OBJDIR)/%.d: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -MM -MT $(@:.d=.o) $< > $@

$(OBJDIR)/%.d: $(TESTDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -MM -MT $(@:.d=.o) $< > $@
