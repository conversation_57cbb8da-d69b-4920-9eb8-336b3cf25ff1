@echo off
REM TCP/IP Stack Build Script for Windows

echo Building TCP/IP Stack...

REM Create directories
if not exist obj mkdir obj
if not exist bin mkdir bin

REM Check for WSL and try to use it
wsl --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Using WSL for compilation...
    wsl bash -c "cd /mnt/c/Users/<USER>/OneDrive/Desktop/TCP && make test_ethernet"
    if %errorlevel% equ 0 (
        echo Build successful!
        goto :end
    ) else (
        echo WSL build failed, trying alternative approach...
    )
)

REM Try to find MinGW or other Windows compilers
where g++ >nul 2>&1
if %errorlevel% equ 0 (
    echo Found g++, compiling with MinGW...
    goto :mingw_build
)

where cl >nul 2>&1
if %errorlevel% equ 0 (
    echo Found MSVC, compiling with Visual Studio...
    goto :msvc_build
)

echo No suitable compiler found. Please install:
echo - MinGW-w64 (recommended)
echo - Visual Studio Build Tools
echo - Or use WSL with build-essential
goto :error

:mingw_build
echo Compiling with MinGW...
g++ -std=c++17 -Wall -Wextra -O2 -g -Iinclude -c src/utils.cpp -o obj/utils.o
if %errorlevel% neq 0 goto :error

g++ -std=c++17 -Wall -Wextra -O2 -g -Iinclude -c src/ethernet.cpp -o obj/ethernet.o
if %errorlevel% neq 0 goto :error

g++ -std=c++17 -Wall -Wextra -O2 -g -Iinclude -c src/tun_tap.cpp -o obj/tun_tap.o
if %errorlevel% neq 0 goto :error

g++ -std=c++17 -Wall -Wextra -O2 -g -Iinclude -c tests/test_ethernet.cpp -o obj/test_ethernet.o
if %errorlevel% neq 0 goto :error

g++ obj/utils.o obj/ethernet.o obj/tun_tap.o obj/test_ethernet.o -o bin/test_ethernet.exe -lpthread
if %errorlevel% neq 0 goto :error

echo Build successful! Test executable: bin/test_ethernet.exe
goto :end

:msvc_build
echo Compiling with MSVC...
cl /std:c++17 /EHsc /Iinclude /c src/utils.cpp /Fo:obj/utils.obj
if %errorlevel% neq 0 goto :error

cl /std:c++17 /EHsc /Iinclude /c src/ethernet.cpp /Fo:obj/ethernet.obj
if %errorlevel% neq 0 goto :error

cl /std:c++17 /EHsc /Iinclude /c src/tun_tap.cpp /Fo:obj/tun_tap.obj
if %errorlevel% neq 0 goto :error

cl /std:c++17 /EHsc /Iinclude /c tests/test_ethernet.cpp /Fo:obj/test_ethernet.obj
if %errorlevel% neq 0 goto :error

link obj/utils.obj obj/ethernet.obj obj/tun_tap.obj obj/test_ethernet.obj /OUT:bin/test_ethernet.exe
if %errorlevel% neq 0 goto :error

echo Build successful! Test executable: bin/test_ethernet.exe
goto :end

:error
echo Build failed!
exit /b 1

:end
echo Done.
