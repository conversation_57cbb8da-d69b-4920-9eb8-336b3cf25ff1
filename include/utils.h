#ifndef UTILS_H
#define UTILS_H

#include <cstdint>
#include <string>
#include <vector>
#include <iostream>
#include <iomanip>

// Network byte order conversion utilities
uint16_t htons_custom(uint16_t hostshort);
uint32_t htonl_custom(uint32_t hostlong);
uint16_t ntohs_custom(uint16_t netshort);
uint32_t ntohl_custom(uint32_t netlong);

// Checksum calculation utilities
uint16_t calculate_checksum(const void* data, size_t length);
uint16_t calculate_ip_checksum(const void* header, size_t length);
uint16_t calculate_tcp_checksum(const void* tcp_header, size_t tcp_length, 
                               uint32_t src_ip, uint32_t dst_ip);
uint16_t calculate_udp_checksum(const void* udp_header, size_t udp_length,
                               uint32_t src_ip, uint32_t dst_ip);

// MAC address utilities
struct MacAddress {
    uint8_t addr[6];
    
    MacAddress();
    MacAddress(const uint8_t* mac);
    MacAddress(const std::string& mac_str);
    
    bool operator==(const MacAddress& other) const;
    bool operator!=(const MacAddress& other) const;
    std::string to_string() const;
    bool is_broadcast() const;
    bool is_zero() const;
};

// IP address utilities
struct IpAddress {
    uint32_t addr;
    
    IpAddress();
    IpAddress(uint32_t ip);
    IpAddress(const std::string& ip_str);
    IpAddress(uint8_t a, uint8_t b, uint8_t c, uint8_t d);
    
    bool operator==(const IpAddress& other) const;
    bool operator!=(const IpAddress& other) const;
    bool operator<(const IpAddress& other) const;
    std::string to_string() const;
    uint32_t to_network_order() const;
    
    static IpAddress from_network_order(uint32_t net_addr);
};

// Logging utilities
enum class LogLevel {
    DEBUG,
    INFO,
    WARNING,
    ERROR
};

class Logger {
public:
    static void set_level(LogLevel level);
    static void log(LogLevel level, const std::string& message);
    static void debug(const std::string& message);
    static void info(const std::string& message);
    static void warning(const std::string& message);
    static void error(const std::string& message);
    
private:
    static LogLevel current_level;
    static std::string level_to_string(LogLevel level);
};

// Hex dump utility for debugging
void hex_dump(const void* data, size_t length, const std::string& prefix = "");

// Time utilities
uint64_t get_current_time_ms();
uint64_t get_current_time_us();

// Buffer utilities
class Buffer {
public:
    Buffer();
    Buffer(size_t initial_size);
    Buffer(const void* data, size_t size);
    
    void append(const void* data, size_t size);
    void prepend(const void* data, size_t size);
    void consume(size_t bytes);
    void clear();
    void resize(size_t new_size);
    
    uint8_t* data() { return buffer_.data(); }
    const uint8_t* data() const { return buffer_.data(); }
    size_t size() const { return buffer_.size(); }
    bool empty() const { return buffer_.empty(); }
    
    uint8_t& operator[](size_t index) { return buffer_[index]; }
    const uint8_t& operator[](size_t index) const { return buffer_[index]; }
    
private:
    std::vector<uint8_t> buffer_;
};

// Random number utilities
uint32_t random_uint32();
uint16_t random_uint16();

// String utilities
std::vector<std::string> split(const std::string& str, char delimiter);
std::string trim(const std::string& str);

#endif // UTILS_H
