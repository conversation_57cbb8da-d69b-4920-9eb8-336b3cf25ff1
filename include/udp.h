#ifndef UDP_H
#define UDP_H

#include "utils.h"
#include "ipv4.h"
#include <functional>
#include <map>
#include <queue>
#include <mutex>
#include <condition_variable>

// UDP constants
constexpr size_t UDP_HEADER_SIZE = 8;
constexpr uint16_t UDP_MIN_PORT = 1;
constexpr uint16_t UDP_MAX_PORT = 65535;

// UDP header structure
struct UdpHeader {
    uint16_t source_port;       // Source port
    uint16_t destination_port;  // Destination port
    uint16_t length;           // Length (header + payload)
    uint16_t checksum;         // Checksum
    
    UdpHeader();
    UdpHeader(uint16_t src_port, uint16_t dst_port, uint16_t payload_length);
    
    // Checksum calculation
    void calculate_checksum(const uint8_t* payload, size_t payload_size,
                           const IpAddress& src_ip, const IpAddress& dst_ip);
    bool verify_checksum(const uint8_t* payload, size_t payload_size,
                        const IpAddress& src_ip, const IpAddress& dst_ip) const;
    
    // Serialization
    void serialize(uint8_t* buffer) const;
    static UdpHeader deserialize(const uint8_t* buffer);
    
    // Validation
    bool is_valid() const;
    uint16_t get_payload_length() const { return length - UDP_HEADER_SIZE; }
    
    // Size
    static constexpr size_t size() { return UDP_HEADER_SIZE; }
    
    std::string to_string() const;
} __attribute__((packed));

// UDP datagram class
class UdpDatagram {
public:
    UdpDatagram();
    UdpDatagram(uint16_t src_port, uint16_t dst_port, 
                const uint8_t* payload, size_t payload_size);
    UdpDatagram(const UdpHeader& header, const uint8_t* payload, size_t payload_size);
    
    // Getters
    const UdpHeader& header() const { return header_; }
    const uint8_t* payload() const { return payload_.data(); }
    size_t payload_size() const { return payload_.size(); }
    size_t total_size() const { return UDP_HEADER_SIZE + payload_.size(); }
    
    uint16_t get_source_port() const { return header_.source_port; }
    uint16_t get_destination_port() const { return header_.destination_port; }
    
    // Setters
    void set_source_port(uint16_t port) { header_.source_port = port; }
    void set_destination_port(uint16_t port) { header_.destination_port = port; }
    void set_payload(const uint8_t* data, size_t size);
    
    // Serialization
    std::vector<uint8_t> serialize(const IpAddress& src_ip, const IpAddress& dst_ip) const;
    bool deserialize(const uint8_t* data, size_t size, 
                    const IpAddress& src_ip, const IpAddress& dst_ip);
    
    // Validation
    bool is_valid(const IpAddress& src_ip, const IpAddress& dst_ip) const;
    
    // Debug
    std::string to_string() const;
    void dump() const;
    
private:
    UdpHeader header_;
    std::vector<uint8_t> payload_;
};

// UDP socket endpoint
struct UdpEndpoint {
    IpAddress ip;
    uint16_t port;
    
    UdpEndpoint();
    UdpEndpoint(const IpAddress& addr, uint16_t p);
    UdpEndpoint(const std::string& ip_str, uint16_t p);
    
    bool operator==(const UdpEndpoint& other) const;
    bool operator!=(const UdpEndpoint& other) const;
    bool operator<(const UdpEndpoint& other) const;
    
    std::string to_string() const;
    bool is_valid() const;
};

// UDP socket class
class UdpSocket {
public:
    UdpSocket();
    ~UdpSocket();
    
    // Socket operations
    bool bind(uint16_t port);
    bool bind(const IpAddress& ip, uint16_t port);
    bool connect(const UdpEndpoint& remote);
    void close();
    
    // Data transmission
    ssize_t send(const uint8_t* data, size_t size);
    ssize_t send_to(const uint8_t* data, size_t size, const UdpEndpoint& destination);
    ssize_t receive(uint8_t* buffer, size_t buffer_size);
    ssize_t receive_from(uint8_t* buffer, size_t buffer_size, UdpEndpoint& source);
    
    // Socket state
    bool is_bound() const { return bound_; }
    bool is_connected() const { return connected_; }
    const UdpEndpoint& get_local_endpoint() const { return local_endpoint_; }
    const UdpEndpoint& get_remote_endpoint() const { return remote_endpoint_; }
    
    // Socket options
    void set_blocking(bool blocking) { blocking_ = blocking; }
    bool is_blocking() const { return blocking_; }
    void set_receive_timeout(std::chrono::milliseconds timeout) { receive_timeout_ = timeout; }
    std::chrono::milliseconds get_receive_timeout() const { return receive_timeout_; }
    
    // Buffer management
    void set_receive_buffer_size(size_t size) { max_receive_buffer_size_ = size; }
    size_t get_receive_buffer_size() const { return max_receive_buffer_size_; }
    size_t get_pending_data_size() const;
    
    // Statistics
    struct Statistics {
        uint64_t bytes_sent = 0;
        uint64_t bytes_received = 0;
        uint64_t datagrams_sent = 0;
        uint64_t datagrams_received = 0;
        uint64_t send_errors = 0;
        uint64_t receive_errors = 0;
    };
    
    const Statistics& get_statistics() const { return stats_; }
    void reset_statistics() { stats_ = Statistics{}; }
    
private:
    friend class UdpProtocol;
    
    // Internal data reception (called by UdpProtocol)
    void receive_datagram(const UdpDatagram& datagram, const UdpEndpoint& source);
    
    // Received datagram with source information
    struct ReceivedDatagram {
        UdpDatagram datagram;
        UdpEndpoint source;
        std::chrono::steady_clock::time_point timestamp;
    };
    
    bool bound_;
    bool connected_;
    bool blocking_;
    UdpEndpoint local_endpoint_;
    UdpEndpoint remote_endpoint_;
    
    // Receive buffer
    std::queue<ReceivedDatagram> receive_buffer_;
    size_t max_receive_buffer_size_;
    std::mutex receive_mutex_;
    std::condition_variable receive_cv_;
    std::chrono::milliseconds receive_timeout_;
    
    Statistics stats_;
};

// UDP protocol handler
class UdpProtocol {
public:
    UdpProtocol(Ipv4Protocol& ipv4);
    ~UdpProtocol();
    
    // Socket management
    std::shared_ptr<UdpSocket> create_socket();
    bool bind_socket(std::shared_ptr<UdpSocket> socket, uint16_t port);
    bool bind_socket(std::shared_ptr<UdpSocket> socket, const IpAddress& ip, uint16_t port);
    void close_socket(std::shared_ptr<UdpSocket> socket);
    
    // Datagram transmission
    bool send_datagram(const UdpDatagram& datagram, const IpAddress& src_ip, const IpAddress& dst_ip);
    bool send_datagram(uint16_t src_port, uint16_t dst_port, const uint8_t* payload, size_t payload_size,
                      const IpAddress& src_ip, const IpAddress& dst_ip);
    
    // Packet handling
    void handle_ipv4_packet(const Ipv4Packet& packet);
    
    // Port management
    uint16_t allocate_ephemeral_port();
    bool is_port_in_use(uint16_t port) const;
    
    // Statistics
    struct Statistics {
        uint64_t datagrams_sent = 0;
        uint64_t datagrams_received = 0;
        uint64_t bytes_sent = 0;
        uint64_t bytes_received = 0;
        uint64_t invalid_datagrams = 0;
        uint64_t checksum_errors = 0;
        uint64_t port_unreachable = 0;
        uint64_t buffer_overflows = 0;
    };
    
    const Statistics& get_statistics() const { return stats_; }
    void reset_statistics() { stats_ = Statistics{}; }
    void dump_statistics() const;
    
    // Configuration
    void set_checksum_enabled(bool enabled) { checksum_enabled_ = enabled; }
    bool is_checksum_enabled() const { return checksum_enabled_; }
    
private:
    // Handle received UDP datagram
    void handle_udp_datagram(const UdpDatagram& datagram, const IpAddress& src_ip, const IpAddress& dst_ip);
    
    // Find socket bound to port
    std::shared_ptr<UdpSocket> find_socket(uint16_t port);
    
    // Send ICMP port unreachable
    void send_port_unreachable(const IpAddress& dst_ip, const Ipv4Packet& original);
    
    Ipv4Protocol& ipv4_;
    bool checksum_enabled_;
    Statistics stats_;
    
    // Socket management
    std::map<uint16_t, std::weak_ptr<UdpSocket>> bound_sockets_;
    std::mutex sockets_mutex_;
    
    // Ephemeral port allocation
    uint16_t next_ephemeral_port_;
    static constexpr uint16_t EPHEMERAL_PORT_START = 32768;
    static constexpr uint16_t EPHEMERAL_PORT_END = 65535;
};

// UDP server helper class
class UdpServer {
public:
    UdpServer(UdpProtocol& udp_protocol, uint16_t port);
    ~UdpServer();
    
    // Server operations
    bool start();
    void stop();
    bool is_running() const { return running_; }
    
    // Message handling
    using MessageHandler = std::function<void(const uint8_t*, size_t, const UdpEndpoint&)>;
    void set_message_handler(MessageHandler handler) { message_handler_ = handler; }
    
    // Send response
    bool send_response(const uint8_t* data, size_t size, const UdpEndpoint& destination);
    
    // Configuration
    uint16_t get_port() const { return port_; }
    void set_buffer_size(size_t size) { buffer_size_ = size; }
    
private:
    // Server thread function
    void server_thread();
    
    UdpProtocol& udp_protocol_;
    uint16_t port_;
    std::shared_ptr<UdpSocket> socket_;
    MessageHandler message_handler_;
    
    std::thread server_thread_;
    std::atomic<bool> running_;
    size_t buffer_size_;
};

// Utility functions
bool is_valid_udp_port(uint16_t port);
bool is_valid_udp_datagram(const uint8_t* data, size_t size);
std::string udp_port_to_string(uint16_t port);
std::string udp_endpoint_to_string(const UdpEndpoint& endpoint);
UdpEndpoint parse_udp_endpoint(const std::string& endpoint_str);

#endif // UDP_H
