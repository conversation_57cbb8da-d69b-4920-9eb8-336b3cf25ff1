#ifndef ICMP_H
#define ICMP_H

#include "utils.h"
#include "ipv4.h"
#include <functional>
#include <map>
#include <chrono>

// ICMP message types
constexpr uint8_t ICMP_TYPE_ECHO_REPLY = 0;
constexpr uint8_t ICMP_TYPE_DEST_UNREACHABLE = 3;
constexpr uint8_t ICMP_TYPE_SOURCE_QUENCH = 4;
constexpr uint8_t ICMP_TYPE_REDIRECT = 5;
constexpr uint8_t ICMP_TYPE_ECHO_REQUEST = 8;
constexpr uint8_t ICMP_TYPE_TIME_EXCEEDED = 11;
constexpr uint8_t ICMP_TYPE_PARAM_PROBLEM = 12;
constexpr uint8_t ICMP_TYPE_TIMESTAMP_REQUEST = 13;
constexpr uint8_t ICMP_TYPE_TIMESTAMP_REPLY = 14;
constexpr uint8_t ICMP_TYPE_INFO_REQUEST = 15;
constexpr uint8_t ICMP_TYPE_INFO_REPLY = 16;

// ICMP destination unreachable codes
constexpr uint8_t ICMP_CODE_NET_UNREACHABLE = 0;
constexpr uint8_t ICMP_CODE_HOST_UNREACHABLE = 1;
constexpr uint8_t ICMP_CODE_PROTOCOL_UNREACHABLE = 2;
constexpr uint8_t ICMP_CODE_PORT_UNREACHABLE = 3;
constexpr uint8_t ICMP_CODE_FRAGMENTATION_NEEDED = 4;
constexpr uint8_t ICMP_CODE_SOURCE_ROUTE_FAILED = 5;

// ICMP time exceeded codes
constexpr uint8_t ICMP_CODE_TTL_EXCEEDED = 0;
constexpr uint8_t ICMP_CODE_FRAGMENT_REASSEMBLY_TIMEOUT = 1;

// ICMP header structure
struct IcmpHeader {
    uint8_t type;           // ICMP message type
    uint8_t code;           // ICMP message code
    uint16_t checksum;      // Checksum
    uint32_t rest_of_header; // Rest of header (varies by type)
    
    IcmpHeader();
    IcmpHeader(uint8_t type, uint8_t code, uint32_t rest = 0);
    
    // Checksum calculation
    void calculate_checksum(const uint8_t* payload, size_t payload_size);
    bool verify_checksum(const uint8_t* payload, size_t payload_size) const;
    
    // Serialization
    void serialize(uint8_t* buffer) const;
    static IcmpHeader deserialize(const uint8_t* buffer);
    
    // Size
    static constexpr size_t size() { return 8; }
    
    std::string to_string() const;
} __attribute__((packed));

// ICMP Echo header (for ping)
struct IcmpEchoHeader {
    uint8_t type;           // ICMP_TYPE_ECHO_REQUEST or ICMP_TYPE_ECHO_REPLY
    uint8_t code;           // Always 0 for echo
    uint16_t checksum;      // Checksum
    uint16_t identifier;    // Identifier
    uint16_t sequence;      // Sequence number
    
    IcmpEchoHeader();
    IcmpEchoHeader(uint8_t type, uint16_t id, uint16_t seq);
    
    // Checksum calculation
    void calculate_checksum(const uint8_t* payload, size_t payload_size);
    bool verify_checksum(const uint8_t* payload, size_t payload_size) const;
    
    // Serialization
    void serialize(uint8_t* buffer) const;
    static IcmpEchoHeader deserialize(const uint8_t* buffer);
    
    // Size
    static constexpr size_t size() { return 8; }
    
    std::string to_string() const;
} __attribute__((packed));

// ICMP message class
class IcmpMessage {
public:
    IcmpMessage();
    IcmpMessage(uint8_t type, uint8_t code, const uint8_t* payload = nullptr, size_t payload_size = 0);
    IcmpMessage(const IcmpHeader& header, const uint8_t* payload = nullptr, size_t payload_size = 0);
    
    // Getters
    const IcmpHeader& header() const { return header_; }
    const uint8_t* payload() const { return payload_.data(); }
    size_t payload_size() const { return payload_.size(); }
    size_t total_size() const { return IcmpHeader::size() + payload_.size(); }
    
    uint8_t get_type() const { return header_.type; }
    uint8_t get_code() const { return header_.code; }
    
    // Setters
    void set_type(uint8_t type) { header_.type = type; }
    void set_code(uint8_t code) { header_.code = code; }
    void set_rest_of_header(uint32_t rest) { header_.rest_of_header = rest; }
    void set_payload(const uint8_t* data, size_t size);
    
    // Serialization
    std::vector<uint8_t> serialize() const;
    bool deserialize(const uint8_t* data, size_t size);
    
    // Validation
    bool is_valid() const;
    
    // Message type checks
    bool is_echo_request() const { return header_.type == ICMP_TYPE_ECHO_REQUEST; }
    bool is_echo_reply() const { return header_.type == ICMP_TYPE_ECHO_REPLY; }
    bool is_dest_unreachable() const { return header_.type == ICMP_TYPE_DEST_UNREACHABLE; }
    bool is_time_exceeded() const { return header_.type == ICMP_TYPE_TIME_EXCEEDED; }
    
    // Debug
    std::string to_string() const;
    void dump() const;
    
private:
    IcmpHeader header_;
    std::vector<uint8_t> payload_;
};

// ICMP Echo (ping) message
class IcmpEchoMessage {
public:
    IcmpEchoMessage();
    IcmpEchoMessage(uint8_t type, uint16_t identifier, uint16_t sequence,
                    const uint8_t* payload = nullptr, size_t payload_size = 0);
    
    // Getters
    const IcmpEchoHeader& header() const { return header_; }
    const uint8_t* payload() const { return payload_.data(); }
    size_t payload_size() const { return payload_.size(); }
    size_t total_size() const { return IcmpEchoHeader::size() + payload_.size(); }
    
    uint8_t get_type() const { return header_.type; }
    uint16_t get_identifier() const { return header_.identifier; }
    uint16_t get_sequence() const { return header_.sequence; }
    
    // Setters
    void set_type(uint8_t type) { header_.type = type; }
    void set_identifier(uint16_t id) { header_.identifier = id; }
    void set_sequence(uint16_t seq) { header_.sequence = seq; }
    void set_payload(const uint8_t* data, size_t size);
    
    // Serialization
    std::vector<uint8_t> serialize() const;
    bool deserialize(const uint8_t* data, size_t size);
    
    // Validation
    bool is_valid() const;
    
    // Create reply from request
    IcmpEchoMessage create_reply() const;
    
    // Debug
    std::string to_string() const;
    void dump() const;
    
private:
    IcmpEchoHeader header_;
    std::vector<uint8_t> payload_;
};

// Ping session for tracking ping requests/replies
struct PingSession {
    uint16_t identifier;
    uint16_t sequence;
    IpAddress target_ip;
    std::chrono::steady_clock::time_point send_time;
    std::function<void(bool, std::chrono::microseconds)> callback;
    
    PingSession();
    PingSession(uint16_t id, uint16_t seq, const IpAddress& target,
                std::function<void(bool, std::chrono::microseconds)> cb);
    
    bool is_expired(std::chrono::milliseconds timeout = std::chrono::milliseconds(5000)) const;
    std::chrono::microseconds get_rtt() const;
};

// ICMP protocol handler
class IcmpProtocol {
public:
    IcmpProtocol(Ipv4Protocol& ipv4);
    ~IcmpProtocol();
    
    // Echo (ping) operations
    bool send_echo_request(const IpAddress& target, uint16_t identifier, uint16_t sequence,
                          const uint8_t* payload = nullptr, size_t payload_size = 0);
    bool send_echo_reply(const IpAddress& target, uint16_t identifier, uint16_t sequence,
                        const uint8_t* payload = nullptr, size_t payload_size = 0);
    
    // Ping with callback
    bool ping(const IpAddress& target, std::function<void(bool, std::chrono::microseconds)> callback,
              const uint8_t* payload = nullptr, size_t payload_size = 0);
    
    // Error message generation
    bool send_dest_unreachable(const IpAddress& target, uint8_t code, const Ipv4Packet& original);
    bool send_time_exceeded(const IpAddress& target, uint8_t code, const Ipv4Packet& original);
    bool send_parameter_problem(const IpAddress& target, uint8_t pointer, const Ipv4Packet& original);
    
    // Packet handling
    void handle_ipv4_packet(const Ipv4Packet& packet);
    
    // Configuration
    void set_auto_reply_echo(bool enable) { auto_reply_echo_ = enable; }
    bool get_auto_reply_echo() const { return auto_reply_echo_; }
    
    // Statistics
    struct Statistics {
        uint64_t echo_requests_sent = 0;
        uint64_t echo_requests_received = 0;
        uint64_t echo_replies_sent = 0;
        uint64_t echo_replies_received = 0;
        uint64_t dest_unreachable_sent = 0;
        uint64_t dest_unreachable_received = 0;
        uint64_t time_exceeded_sent = 0;
        uint64_t time_exceeded_received = 0;
        uint64_t invalid_messages = 0;
        uint64_t checksum_errors = 0;
    };
    
    const Statistics& get_statistics() const { return stats_; }
    void reset_statistics() { stats_ = Statistics{}; }
    void dump_statistics() const;
    
    // Message handlers
    using MessageHandler = std::function<void(const IcmpMessage&, const IpAddress&)>;
    void set_echo_request_handler(MessageHandler handler) { echo_request_handler_ = handler; }
    void set_echo_reply_handler(MessageHandler handler) { echo_reply_handler_ = handler; }
    void set_dest_unreachable_handler(MessageHandler handler) { dest_unreachable_handler_ = handler; }
    void set_time_exceeded_handler(MessageHandler handler) { time_exceeded_handler_ = handler; }
    
private:
    // Handle specific ICMP message types
    void handle_echo_request(const IcmpEchoMessage& message, const IpAddress& source);
    void handle_echo_reply(const IcmpEchoMessage& message, const IpAddress& source);
    void handle_dest_unreachable(const IcmpMessage& message, const IpAddress& source);
    void handle_time_exceeded(const IcmpMessage& message, const IpAddress& source);
    
    // Send ICMP message
    bool send_icmp_message(const IpAddress& target, const IcmpMessage& message);
    
    // Ping session management
    void add_ping_session(const PingSession& session);
    void handle_ping_reply(uint16_t identifier, uint16_t sequence);
    void cleanup_expired_sessions();
    
    Ipv4Protocol& ipv4_;
    bool auto_reply_echo_;
    Statistics stats_;
    
    // Message handlers
    MessageHandler echo_request_handler_;
    MessageHandler echo_reply_handler_;
    MessageHandler dest_unreachable_handler_;
    MessageHandler time_exceeded_handler_;
    
    // Ping session tracking
    std::map<std::pair<uint16_t, uint16_t>, PingSession> ping_sessions_; // (id, seq) -> session
    std::mutex sessions_mutex_;
    uint16_t next_ping_id_;
    uint16_t next_ping_sequence_;
    
    // Cleanup thread
    std::thread cleanup_thread_;
    std::atomic<bool> cleanup_running_;
    void cleanup_thread_func();
};

// Utility functions
std::string icmp_type_to_string(uint8_t type);
std::string icmp_code_to_string(uint8_t type, uint8_t code);
bool is_valid_icmp_message(const uint8_t* data, size_t size);

#endif // ICMP_H
