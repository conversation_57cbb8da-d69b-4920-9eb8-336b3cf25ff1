#ifndef ARP_H
#define ARP_H

#include "utils.h"
#include "ethernet.h"
#include <map>
#include <mutex>
#include <chrono>
#include <functional>
#include <thread>
#include <atomic>

// ARP constants
constexpr uint16_t ARP_HARDWARE_ETHERNET = 1;
constexpr uint16_t ARP_PROTOCOL_IPV4 = 0x0800;
constexpr uint8_t ARP_HARDWARE_SIZE = 6;
constexpr uint8_t ARP_PROTOCOL_SIZE = 4;

// ARP operation codes
constexpr uint16_t ARP_OP_REQUEST = 1;
constexpr uint16_t ARP_OP_REPLY = 2;

// ARP header structure
struct ArpHeader {
    uint16_t hardware_type;     // Hardware type (Ethernet = 1)
    uint16_t protocol_type;     // Protocol type (IPv4 = 0x0800)
    uint8_t hardware_size;      // Hardware address size (6 for Ethernet)
    uint8_t protocol_size;      // Protocol address size (4 for IPv4)
    uint16_t operation;         // Operation (request = 1, reply = 2)
    Mac<PERSON>ddress sender_mac;      // Sender hardware address
    IpAddress sender_ip;        // Sender protocol address
    MacAddress target_mac;      // Target hardware address
    IpAddress target_ip;        // Target protocol address
    
    ArpHeader();
    ArpHeader(uint16_t op, const MacAddress& sender_mac, const IpAddress& sender_ip,
              const MacAddress& target_mac, const IpAddress& target_ip);
    
    // Serialization
    void serialize(uint8_t* buffer) const;
    static ArpHeader deserialize(const uint8_t* buffer);
    
    // Validation
    bool is_valid() const;
    bool is_request() const { return operation == ARP_OP_REQUEST; }
    bool is_reply() const { return operation == ARP_OP_REPLY; }
    
    // Size
    static constexpr size_t size() { return 28; }
    
    std::string to_string() const;
} __attribute__((packed));

// ARP table entry
struct ArpEntry {
    MacAddress mac_address;
    std::chrono::steady_clock::time_point timestamp;
    bool is_static;
    
    ArpEntry();
    ArpEntry(const MacAddress& mac, bool static_entry = false);
    
    bool is_expired(std::chrono::seconds timeout = std::chrono::seconds(300)) const;
    std::string to_string() const;
};

// ARP table class
class ArpTable {
public:
    ArpTable();
    
    // Entry management
    void add_entry(const IpAddress& ip, const MacAddress& mac, bool is_static = false);
    bool remove_entry(const IpAddress& ip);
    void clear_dynamic_entries();
    void clear_all_entries();
    
    // Lookup
    bool lookup(const IpAddress& ip, MacAddress& mac) const;
    bool has_entry(const IpAddress& ip) const;
    
    // Maintenance
    void cleanup_expired_entries(std::chrono::seconds timeout = std::chrono::seconds(300));
    size_t size() const;
    
    // Iteration
    std::vector<std::pair<IpAddress, ArpEntry>> get_all_entries() const;
    
    // Debug
    void dump() const;
    std::string to_string() const;
    
private:
    mutable std::mutex mutex_;
    std::map<IpAddress, ArpEntry> entries_;
};

// ARP protocol handler
class ArpProtocol {
public:
    ArpProtocol(EthernetInterface& interface, const IpAddress& local_ip);
    ~ArpProtocol();
    
    // Configuration
    void set_local_ip(const IpAddress& ip) { local_ip_ = ip; }
    const IpAddress& get_local_ip() const { return local_ip_; }
    
    // ARP operations
    bool send_request(const IpAddress& target_ip);
    bool send_reply(const IpAddress& target_ip, const MacAddress& target_mac);
    bool send_gratuitous_arp();
    
    // Address resolution
    bool resolve_address(const IpAddress& ip, MacAddress& mac, 
                        std::chrono::milliseconds timeout = std::chrono::milliseconds(1000));
    
    // Table access
    ArpTable& get_table() { return arp_table_; }
    const ArpTable& get_table() const { return arp_table_; }
    
    // Packet handling
    void handle_ethernet_frame(const EthernetFrame& frame);
    
    // Statistics
    struct Statistics {
        uint64_t requests_sent = 0;
        uint64_t requests_received = 0;
        uint64_t replies_sent = 0;
        uint64_t replies_received = 0;
        uint64_t invalid_packets = 0;
        uint64_t cache_hits = 0;
        uint64_t cache_misses = 0;
    };
    
    const Statistics& get_statistics() const { return stats_; }
    void reset_statistics() { stats_ = Statistics{}; }
    void dump_statistics() const;
    
    // Callbacks
    using ResolveCallback = std::function<void(const IpAddress&, const MacAddress&, bool)>;
    void set_resolve_callback(ResolveCallback callback) { resolve_callback_ = callback; }
    
private:
    // Handle ARP packets
    void handle_arp_request(const ArpHeader& arp_header);
    void handle_arp_reply(const ArpHeader& arp_header);
    
    // Send ARP packet
    bool send_arp_packet(const ArpHeader& arp_header);
    
    EthernetInterface& interface_;
    IpAddress local_ip_;
    ArpTable arp_table_;
    Statistics stats_;
    ResolveCallback resolve_callback_;
    
    // Pending requests for address resolution
    struct PendingRequest {
        std::chrono::steady_clock::time_point timestamp;
        std::function<void(bool, const MacAddress&)> callback;
    };
    
    mutable std::mutex pending_mutex_;
    std::map<IpAddress, PendingRequest> pending_requests_;
    
    // Cleanup thread
    std::thread cleanup_thread_;
    std::atomic<bool> cleanup_running_;
    void cleanup_thread_func();
};

// Utility functions
std::string arp_operation_to_string(uint16_t operation);
bool is_valid_arp_packet(const uint8_t* data, size_t size);

#endif // ARP_H
