#ifndef IPV4_H
#define IPV4_H

#include "utils.h"
#include "ethernet.h"
#include <functional>
#include <map>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <chrono>

// IPv4 constants
constexpr uint8_t IPV4_VERSION = 4;
constexpr uint8_t IPV4_MIN_HEADER_SIZE = 20;
constexpr uint8_t IPV4_MAX_HEADER_SIZE = 60;
constexpr uint16_t IPV4_MAX_PACKET_SIZE = 65535;
constexpr uint16_t IPV4_DEFAULT_TTL = 64;

// IPv4 protocol numbers
constexpr uint8_t IPV4_PROTOCOL_ICMP = 1;
constexpr uint8_t IPV4_PROTOCOL_TCP = 6;
constexpr uint8_t IPV4_PROTOCOL_UDP = 17;

// IPv4 flags
constexpr uint16_t IPV4_FLAG_DONT_FRAGMENT = 0x4000;
constexpr uint16_t IPV4_FLAG_MORE_FRAGMENTS = 0x2000;
constexpr uint16_t IPV4_FRAGMENT_OFFSET_MASK = 0x1FFF;

// IPv4 header structure
struct Ipv4Header {
    uint8_t version_ihl;        // Version (4 bits) + IHL (4 bits)
    uint8_t type_of_service;    // Type of Service
    uint16_t total_length;      // Total Length
    uint16_t identification;    // Identification
    uint16_t flags_fragment;    // Flags (3 bits) + Fragment Offset (13 bits)
    uint8_t time_to_live;       // Time to Live
    uint8_t protocol;           // Protocol
    uint16_t header_checksum;   // Header Checksum
    IpAddress source_ip;        // Source Address
    IpAddress destination_ip;   // Destination Address
    
    Ipv4Header();
    Ipv4Header(const IpAddress& src, const IpAddress& dst, uint8_t protocol, 
               uint16_t payload_length, uint8_t ttl = IPV4_DEFAULT_TTL);
    
    // Header field accessors
    uint8_t get_version() const { return (version_ihl >> 4) & 0x0F; }
    uint8_t get_ihl() const { return version_ihl & 0x0F; }
    uint8_t get_header_length() const { return get_ihl() * 4; }
    uint16_t get_flags() const { return (flags_fragment >> 13) & 0x07; }
    uint16_t get_fragment_offset() const { return flags_fragment & IPV4_FRAGMENT_OFFSET_MASK; }
    uint16_t get_payload_length() const { return total_length - get_header_length(); }
    
    void set_version(uint8_t version) { version_ihl = (version << 4) | (version_ihl & 0x0F); }
    void set_ihl(uint8_t ihl) { version_ihl = (version_ihl & 0xF0) | (ihl & 0x0F); }
    void set_flags(uint16_t flags) { flags_fragment = (flags << 13) | (flags_fragment & IPV4_FRAGMENT_OFFSET_MASK); }
    void set_fragment_offset(uint16_t offset) { flags_fragment = (flags_fragment & 0xE000) | (offset & IPV4_FRAGMENT_OFFSET_MASK); }

    // Flag setters
    void set_dont_fragment(bool flag) {
        if (flag) flags_fragment |= IPV4_FLAG_DONT_FRAGMENT;
        else flags_fragment &= ~IPV4_FLAG_DONT_FRAGMENT;
    }
    void set_more_fragments(bool flag) {
        if (flag) flags_fragment |= IPV4_FLAG_MORE_FRAGMENTS;
        else flags_fragment &= ~IPV4_FLAG_MORE_FRAGMENTS;
    }
    
    // Checksum calculation
    void calculate_checksum();
    bool verify_checksum() const;
    
    // Serialization
    void serialize(uint8_t* buffer) const;
    static Ipv4Header deserialize(const uint8_t* buffer);
    
    // Validation
    bool is_valid() const;
    bool is_fragmented() const;
    bool has_more_fragments() const;
    bool dont_fragment() const;
    
    std::string to_string() const;
} __attribute__((packed));

// IPv4 packet class
class Ipv4Packet {
public:
    Ipv4Packet();
    Ipv4Packet(const IpAddress& src, const IpAddress& dst, uint8_t protocol,
               const uint8_t* payload, size_t payload_size, uint8_t ttl = IPV4_DEFAULT_TTL);
    
    // Getters
    const Ipv4Header& header() const { return header_; }
    const uint8_t* payload() const { return payload_.data(); }
    size_t payload_size() const { return payload_.size(); }
    size_t total_size() const { return header_.get_header_length() + payload_.size(); }
    
    // Setters
    void set_source_ip(const IpAddress& ip) { header_.source_ip = ip; }
    void set_destination_ip(const IpAddress& ip) { header_.destination_ip = ip; }
    void set_protocol(uint8_t protocol) { header_.protocol = protocol; }
    void set_ttl(uint8_t ttl) { header_.time_to_live = ttl; }
    void set_payload(const uint8_t* data, size_t size);
    
    // Serialization
    std::vector<uint8_t> serialize() const;
    bool deserialize(const uint8_t* data, size_t size);
    
    // Validation
    bool is_valid() const;
    
    // Fragmentation
    std::vector<Ipv4Packet> fragment(size_t mtu) const;
    static Ipv4Packet reassemble(const std::vector<Ipv4Packet>& fragments);
    
    // Debug
    std::string to_string() const;
    void dump() const;
    
private:
    Ipv4Header header_;
    std::vector<uint8_t> payload_;
};

// IPv4 routing table entry
struct RouteEntry {
    IpAddress network;
    IpAddress netmask;
    IpAddress gateway;
    std::string interface;
    uint32_t metric;
    
    RouteEntry();
    RouteEntry(const IpAddress& net, const IpAddress& mask, 
               const IpAddress& gw, const std::string& iface, uint32_t met = 0);
    
    bool matches(const IpAddress& destination) const;
    std::string to_string() const;
};

// IPv4 routing table
class RoutingTable {
public:
    RoutingTable();
    
    // Route management
    void add_route(const RouteEntry& route);
    bool remove_route(const IpAddress& network, const IpAddress& netmask);
    void clear_routes();
    
    // Route lookup
    bool lookup_route(const IpAddress& destination, RouteEntry& route) const;
    IpAddress get_next_hop(const IpAddress& destination) const;
    
    // Default route
    void set_default_route(const IpAddress& gateway, const std::string& interface);
    bool has_default_route() const;
    
    // Table access
    std::vector<RouteEntry> get_all_routes() const;
    size_t size() const;
    
    // Debug
    void dump() const;
    std::string to_string() const;
    
private:
    std::vector<RouteEntry> routes_;
    mutable std::mutex mutex_;
};

// Fragment reassembly
class FragmentReassembler {
public:
    FragmentReassembler();
    
    // Add fragment and check if packet is complete
    bool add_fragment(const Ipv4Packet& fragment, Ipv4Packet& reassembled);
    
    // Cleanup expired fragments
    void cleanup_expired(std::chrono::seconds timeout = std::chrono::seconds(30));
    
    // Statistics
    size_t get_pending_count() const;
    void clear_all();
    
private:
    struct FragmentKey {
        IpAddress source_ip;
        IpAddress destination_ip;
        uint16_t identification;
        uint8_t protocol;
        
        bool operator<(const FragmentKey& other) const;
    };
    
    struct FragmentSet {
        std::map<uint16_t, Ipv4Packet> fragments;  // offset -> fragment
        std::chrono::steady_clock::time_point timestamp;
        uint16_t total_length;
        bool complete;
    };
    
    std::map<FragmentKey, FragmentSet> pending_fragments_;
    mutable std::mutex mutex_;
};

// IPv4 protocol handler
class Ipv4Protocol {
public:
    Ipv4Protocol(EthernetInterface& interface, const IpAddress& local_ip);
    ~Ipv4Protocol();
    
    // Configuration
    void set_local_ip(const IpAddress& ip) { local_ip_ = ip; }
    const IpAddress& get_local_ip() const { return local_ip_; }
    
    // Packet transmission
    bool send_packet(const Ipv4Packet& packet);
    bool send_packet(const IpAddress& dst, uint8_t protocol, 
                    const uint8_t* payload, size_t payload_size);
    
    // Packet handling
    void handle_ethernet_frame(const EthernetFrame& frame);
    
    // Protocol handlers
    using ProtocolHandler = std::function<void(const Ipv4Packet&)>;
    void register_protocol_handler(uint8_t protocol, ProtocolHandler handler);
    void unregister_protocol_handler(uint8_t protocol);
    
    // Routing
    RoutingTable& get_routing_table() { return routing_table_; }
    const RoutingTable& get_routing_table() const { return routing_table_; }
    
    // Statistics
    struct Statistics {
        uint64_t packets_sent = 0;
        uint64_t packets_received = 0;
        uint64_t bytes_sent = 0;
        uint64_t bytes_received = 0;
        uint64_t packets_dropped = 0;
        uint64_t checksum_errors = 0;
        uint64_t fragments_sent = 0;
        uint64_t fragments_received = 0;
        uint64_t reassembly_timeouts = 0;
    };
    
    const Statistics& get_statistics() const { return stats_; }
    void reset_statistics() { stats_ = Statistics{}; }
    void dump_statistics() const;
    
private:
    // Handle received IPv4 packet
    void handle_ipv4_packet(const Ipv4Packet& packet);
    
    // Fragment handling
    bool handle_fragment(const Ipv4Packet& packet);
    
    EthernetInterface& interface_;
    IpAddress local_ip_;
    RoutingTable routing_table_;
    FragmentReassembler reassembler_;
    
    std::map<uint8_t, ProtocolHandler> protocol_handlers_;
    std::mutex handlers_mutex_;
    
    Statistics stats_;
    
    // Cleanup thread for fragment reassembly
    std::thread cleanup_thread_;
    std::atomic<bool> cleanup_running_;
    void cleanup_thread_func();
};

// Utility functions
std::string ipv4_protocol_to_string(uint8_t protocol);
bool is_valid_ipv4_packet(const uint8_t* data, size_t size);
IpAddress calculate_network_address(const IpAddress& ip, const IpAddress& netmask);
IpAddress calculate_broadcast_address(const IpAddress& network, const IpAddress& netmask);

#endif // IPV4_H
