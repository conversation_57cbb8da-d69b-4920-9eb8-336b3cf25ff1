#ifndef TUN_TAP_H
#define TUN_TAP_H

#include "ethernet.h"
#include <string>
#include <thread>
#include <atomic>
#include <functional>
#include <map>
#include <mutex>

// TUN/TAP interface types
enum class TunTapMode {
    TUN,    // Layer 3 (IP) interface
    TAP     // Layer 2 (Ethernet) interface
};

// TUN/TAP interface class
class TunTapInterface : public EthernetInterface {
public:
    TunTapInterface(const std::string& device_name, TunTapMode mode, 
                    const MacAddress& mac_addr = MacAddress());
    ~TunTapInterface();
    
    // Interface management
    bool open();
    void close();
    bool is_open() const { return fd_ >= 0; }
    
    // Configuration
    bool set_ip_address(const IpAddress& ip, int prefix_length = 24);
    bool set_mtu(int mtu);
    bool bring_up();
    bool bring_down();
    
    // Frame transmission (inherited from EthernetInterface)
    bool send_frame(const EthernetFrame& frame) override;
    
    // Raw packet transmission (for TUN mode)
    bool send_packet(const uint8_t* data, size_t size);
    
    // Start/stop packet reception thread
    void start_reception();
    void stop_reception();
    
    // Device information
    const std::string& get_device_name() const { return device_name_; }
    TunTapMode get_mode() const { return mode_; }
    int get_mtu() const { return mtu_; }
    
    // Statistics
    void dump_statistics() const;
    
private:
    // Reception thread function
    void reception_thread();
    
    // Handle received data
    void handle_received_data(const uint8_t* data, size_t size);
    
    std::string device_name_;
    TunTapMode mode_;
    int fd_;
    int mtu_;
    
    // Reception thread
    std::thread reception_thread_;
    std::atomic<bool> reception_running_;
    
    // Buffer for reception
    static constexpr size_t BUFFER_SIZE = 2048;
    uint8_t reception_buffer_[BUFFER_SIZE];
};

// TUN/TAP utility functions
class TunTapUtils {
public:
    // Create a TUN/TAP device
    static bool create_device(const std::string& device_name, TunTapMode mode);
    
    // Delete a TUN/TAP device
    static bool delete_device(const std::string& device_name);
    
    // Set device IP address
    static bool set_device_ip(const std::string& device_name, 
                             const IpAddress& ip, int prefix_length = 24);
    
    // Set device MTU
    static bool set_device_mtu(const std::string& device_name, int mtu);
    
    // Bring device up/down
    static bool bring_device_up(const std::string& device_name);
    static bool bring_device_down(const std::string& device_name);
    
    // Check if device exists
    static bool device_exists(const std::string& device_name);
    
    // Get device information
    static bool get_device_info(const std::string& device_name, 
                               IpAddress& ip, int& prefix_length, int& mtu);
    
    // List all TUN/TAP devices
    static std::vector<std::string> list_devices();
    
private:
    // Execute system command
    static bool execute_command(const std::string& command);
    
    // Parse IP command output
    static bool parse_ip_output(const std::string& output, 
                               IpAddress& ip, int& prefix_length);
};

// TUN/TAP manager for multiple interfaces
class TunTapManager {
public:
    static TunTapManager& instance();
    
    // Interface management
    std::shared_ptr<TunTapInterface> create_interface(const std::string& name, 
                                                     TunTapMode mode,
                                                     const MacAddress& mac = MacAddress());
    
    bool remove_interface(const std::string& name);
    std::shared_ptr<TunTapInterface> get_interface(const std::string& name);
    
    // Cleanup all interfaces
    void cleanup_all();
    
    // List active interfaces
    std::vector<std::string> list_interfaces() const;
    
private:
    TunTapManager() = default;
    ~TunTapManager();
    
    std::map<std::string, std::shared_ptr<TunTapInterface>> interfaces_;
    std::mutex interfaces_mutex_;
};

#endif // TUN_TAP_H
