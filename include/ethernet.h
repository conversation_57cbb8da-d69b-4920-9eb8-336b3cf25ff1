#ifndef ETHERNET_H
#define ETHERNET_H

#include "utils.h"
#include <cstdint>
#include <memory>
#include <functional>
#include <vector>

// Ethernet frame constants
constexpr size_t ETHERNET_HEADER_SIZE = 14;
constexpr size_t ETHERNET_MIN_FRAME_SIZE = 64;
constexpr size_t ETHERNET_MAX_FRAME_SIZE = 1518;
constexpr size_t ETHERNET_MTU = 1500;

// EtherType values
constexpr uint16_t ETHERTYPE_IPV4 = 0x0800;
constexpr uint16_t ETHERTYPE_ARP = 0x0806;
constexpr uint16_t ETHERTYPE_IPV6 = 0x86DD;

// Ethernet header structure
struct EthernetHeader {
    MacAddress dst_mac;     // Destination MAC address
    MacAddress src_mac;     // Source MAC address
    uint16_t ethertype;     // EtherType field
    
    EthernetHeader();
    EthernetHeader(const <PERSON><PERSON><PERSON><PERSON>& dst, const <PERSON><PERSON><PERSON><PERSON>& src, uint16_t type);
    
    // Serialize to network byte order
    void serialize(uint8_t* buffer) const;
    
    // Deserialize from network byte order
    static EthernetHeader deserialize(const uint8_t* buffer);
    
    // Get header size
    static constexpr size_t size() { return ETHERNET_HEADER_SIZE; }
    
    std::string to_string() const;
} __attribute__((packed));

// Ethernet frame class
class EthernetFrame {
public:
    EthernetFrame();
    EthernetFrame(const MacAddress& dst_mac, const MacAddress& src_mac, 
                  uint16_t ethertype, const uint8_t* payload, size_t payload_size);
    
    // Getters
    const EthernetHeader& header() const { return header_; }
    const uint8_t* payload() const { return payload_.data(); }
    size_t payload_size() const { return payload_.size(); }
    size_t total_size() const { return ETHERNET_HEADER_SIZE + payload_.size(); }
    
    // Setters
    void set_dst_mac(const MacAddress& mac) { header_.dst_mac = mac; }
    void set_src_mac(const MacAddress& mac) { header_.src_mac = mac; }
    void set_ethertype(uint16_t type) { header_.ethertype = type; }
    void set_payload(const uint8_t* data, size_t size);
    
    // Serialization
    std::vector<uint8_t> serialize() const;
    bool deserialize(const uint8_t* data, size_t size);
    
    // Validation
    bool is_valid() const;
    bool is_broadcast() const;
    bool is_multicast() const;
    
    // Debug
    std::string to_string() const;
    void dump() const;
    
private:
    EthernetHeader header_;
    std::vector<uint8_t> payload_;
};

// Ethernet interface class
class EthernetInterface {
public:
    EthernetInterface(const MacAddress& mac_addr);
    virtual ~EthernetInterface() = default;
    
    // MAC address management
    const MacAddress& get_mac_address() const { return mac_address_; }
    void set_mac_address(const MacAddress& mac) { mac_address_ = mac; }
    
    // Frame transmission
    virtual bool send_frame(const EthernetFrame& frame) = 0;
    
    // Frame reception callback
    using FrameHandler = std::function<void(const EthernetFrame&)>;
    void set_frame_handler(FrameHandler handler) { frame_handler_ = handler; }
    
    // Statistics
    struct Statistics {
        uint64_t frames_sent = 0;
        uint64_t frames_received = 0;
        uint64_t bytes_sent = 0;
        uint64_t bytes_received = 0;
        uint64_t frames_dropped = 0;
        uint64_t crc_errors = 0;
    };
    
    const Statistics& get_statistics() const { return stats_; }
    void reset_statistics() { stats_ = Statistics{}; }
    
protected:
    // Called when a frame is received
    void handle_received_frame(const EthernetFrame& frame);
    
    MacAddress mac_address_;
    FrameHandler frame_handler_;
    Statistics stats_;
};

// Utility functions
bool is_valid_mac_address(const std::string& mac_str);
MacAddress generate_random_mac();
std::string ethertype_to_string(uint16_t ethertype);

#endif // ETHERNET_H
