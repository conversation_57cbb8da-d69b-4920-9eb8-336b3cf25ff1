#ifndef SOCKET_H
#define SOCKET_H

#include "utils.h"
#include "udp.h"
#include "tcp.h"
#include <memory>
#include <functional>

// Socket types
enum class SocketType {
    UDP,
    TCP
};

// Socket address family
enum class AddressFamily {
    INET    // IPv4
};

// Generic socket address
struct SocketAddress {
    AddressFamily family;
    IpAddress ip;
    uint16_t port;
    
    SocketAddress();
    SocketAddress(AddressFamily af, const IpAddress& addr, uint16_t p);
    SocketAddress(const std::string& ip_str, uint16_t p);
    
    bool operator==(const SocketAddress& other) const;
    bool operator!=(const SocketAddress& other) const;
    
    std::string to_string() const;
    bool is_valid() const;
    
    // Conversion to protocol-specific endpoints
    UdpEndpoint to_udp_endpoint() const;
    TcpEndpoint to_tcp_endpoint() const;
    
    // Create from protocol-specific endpoints
    static SocketAddress from_udp_endpoint(const UdpEndpoint& endpoint);
    static SocketAddress from_tcp_endpoint(const TcpEndpoint& endpoint);
};

// Generic socket interface
class Socket {
public:
    Socket(SocketType type, AddressFamily family = AddressFamily::INET);
    virtual ~Socket() = default;
    
    // Socket operations
    virtual bool bind(const SocketAddress& address) = 0;
    virtual bool bind(uint16_t port) = 0;
    virtual void close() = 0;
    
    // Socket information
    SocketType get_type() const { return type_; }
    AddressFamily get_family() const { return family_; }
    virtual bool is_bound() const = 0;
    virtual bool is_closed() const = 0;
    virtual SocketAddress get_local_address() const = 0;
    
    // Socket options
    virtual void set_blocking(bool blocking) = 0;
    virtual bool is_blocking() const = 0;
    virtual void set_receive_timeout(std::chrono::milliseconds timeout) = 0;
    virtual std::chrono::milliseconds get_receive_timeout() const = 0;
    
    // Buffer management
    virtual void set_receive_buffer_size(size_t size) = 0;
    virtual size_t get_receive_buffer_size() const = 0;
    virtual size_t get_pending_receive_data() const = 0;
    
protected:
    SocketType type_;
    AddressFamily family_;
};

// UDP socket wrapper
class UdpSocketWrapper : public Socket {
public:
    UdpSocketWrapper(std::shared_ptr<UdpSocket> udp_socket);
    ~UdpSocketWrapper() override;
    
    // Socket operations
    bool bind(const SocketAddress& address) override;
    bool bind(uint16_t port) override;
    void close() override;
    
    // UDP-specific operations
    bool connect(const SocketAddress& remote);
    ssize_t send(const uint8_t* data, size_t size);
    ssize_t send_to(const uint8_t* data, size_t size, const SocketAddress& destination);
    ssize_t receive(uint8_t* buffer, size_t buffer_size);
    ssize_t receive_from(uint8_t* buffer, size_t buffer_size, SocketAddress& source);
    
    // Socket information
    bool is_bound() const override;
    bool is_closed() const override;
    bool is_connected() const;
    SocketAddress get_local_address() const override;
    SocketAddress get_remote_address() const;
    
    // Socket options
    void set_blocking(bool blocking) override;
    bool is_blocking() const override;
    void set_receive_timeout(std::chrono::milliseconds timeout) override;
    std::chrono::milliseconds get_receive_timeout() const override;
    void set_send_timeout(std::chrono::milliseconds timeout);
    std::chrono::milliseconds get_send_timeout() const;
    
    // Buffer management
    void set_receive_buffer_size(size_t size) override;
    size_t get_receive_buffer_size() const override;
    void set_send_buffer_size(size_t size);
    size_t get_send_buffer_size() const;
    size_t get_pending_receive_data() const override;
    size_t get_pending_send_data() const;
    
    // Statistics
    const UdpSocket::Statistics& get_statistics() const;
    void reset_statistics();
    
    // Access to underlying UDP socket
    std::shared_ptr<UdpSocket> get_udp_socket() const { return udp_socket_; }
    
private:
    std::shared_ptr<UdpSocket> udp_socket_;
};

// TCP socket wrapper
class TcpSocketWrapper : public Socket {
public:
    TcpSocketWrapper(std::shared_ptr<TcpSocket> tcp_socket);
    ~TcpSocketWrapper() override;
    
    // Socket operations
    bool bind(const SocketAddress& address) override;
    bool bind(uint16_t port) override;
    void close() override;
    
    // TCP-specific operations
    bool listen(int backlog = 5);
    std::shared_ptr<TcpSocketWrapper> accept();
    bool connect(const SocketAddress& remote);
    ssize_t send(const uint8_t* data, size_t size);
    ssize_t receive(uint8_t* buffer, size_t buffer_size);
    
    // Socket information
    bool is_bound() const override;
    bool is_closed() const override;
    bool is_listening() const;
    bool is_connected() const;
    TcpState get_state() const;
    SocketAddress get_local_address() const override;
    SocketAddress get_remote_address() const;
    
    // Socket options
    void set_blocking(bool blocking) override;
    bool is_blocking() const override;
    void set_receive_timeout(std::chrono::milliseconds timeout) override;
    std::chrono::milliseconds get_receive_timeout() const override;
    void set_send_timeout(std::chrono::milliseconds timeout);
    std::chrono::milliseconds get_send_timeout() const;
    
    // Buffer management
    void set_receive_buffer_size(size_t size) override;
    size_t get_receive_buffer_size() const override;
    void set_send_buffer_size(size_t size);
    size_t get_send_buffer_size() const;
    size_t get_pending_receive_data() const override;
    size_t get_pending_send_data() const;
    
    // Statistics
    const TcpSocket::Statistics& get_statistics() const;
    void reset_statistics();
    
    // Access to underlying TCP socket
    std::shared_ptr<TcpSocket> get_tcp_socket() const { return tcp_socket_; }
    
private:
    std::shared_ptr<TcpSocket> tcp_socket_;
};

// Socket factory
class SocketFactory {
public:
    // Create sockets
    static std::shared_ptr<UdpSocketWrapper> create_udp_socket();
    static std::shared_ptr<TcpSocketWrapper> create_tcp_socket();
    static std::shared_ptr<Socket> create_socket(SocketType type, AddressFamily family = AddressFamily::INET);
    
    // Protocol access (for internal use)
    static void set_udp_protocol(UdpProtocol* udp);
    static void set_tcp_protocol(TcpProtocol* tcp);
    
private:
    static UdpProtocol* udp_protocol_;
    static TcpProtocol* tcp_protocol_;
};

// Socket utilities
class SocketUtils {
public:
    // Address parsing
    static SocketAddress parse_address(const std::string& address_str);
    static bool is_valid_address_string(const std::string& address_str);
    
    // Port utilities
    static bool is_valid_port(uint16_t port);
    static bool is_ephemeral_port(uint16_t port);
    static bool is_well_known_port(uint16_t port);
    
    // Network utilities
    static std::vector<IpAddress> resolve_hostname(const std::string& hostname);
    static std::string get_hostname_from_ip(const IpAddress& ip);
    
    // Socket type utilities
    static std::string socket_type_to_string(SocketType type);
    static std::string address_family_to_string(AddressFamily family);
};

// Socket server base class
template<typename SocketType>
class SocketServer {
public:
    using ConnectionHandler = std::function<void(std::shared_ptr<SocketType>)>;
    
    SocketServer(uint16_t port) : port_(port), running_(false) {}
    virtual ~SocketServer() { stop(); }
    
    // Server operations
    virtual bool start() = 0;
    virtual void stop() = 0;
    bool is_running() const { return running_; }
    
    // Configuration
    uint16_t get_port() const { return port_; }
    void set_connection_handler(ConnectionHandler handler) { connection_handler_ = handler; }
    
protected:
    uint16_t port_;
    std::atomic<bool> running_;
    ConnectionHandler connection_handler_;
    std::thread server_thread_;
};

// UDP server
class UdpSocketServer : public SocketServer<UdpSocketWrapper> {
public:
    UdpSocketServer(uint16_t port);
    ~UdpSocketServer() override;
    
    bool start() override;
    void stop() override;
    
    // UDP-specific message handling
    using MessageHandler = std::function<void(const uint8_t*, size_t, const SocketAddress&)>;
    void set_message_handler(MessageHandler handler) { message_handler_ = handler; }
    
    // Send response
    bool send_response(const uint8_t* data, size_t size, const SocketAddress& destination);
    
private:
    void server_thread_func();
    
    std::shared_ptr<UdpSocketWrapper> socket_;
    MessageHandler message_handler_;
    static constexpr size_t BUFFER_SIZE = 2048;
};

// TCP server
class TcpSocketServer : public SocketServer<TcpSocketWrapper> {
public:
    TcpSocketServer(uint16_t port);
    ~TcpSocketServer() override;
    
    bool start() override;
    void stop() override;
    
    // Configuration
    void set_backlog(int backlog) { backlog_ = backlog; }
    int get_backlog() const { return backlog_; }
    
private:
    void server_thread_func();
    void handle_client(std::shared_ptr<TcpSocketWrapper> client);
    
    std::shared_ptr<TcpSocketWrapper> listen_socket_;
    int backlog_;
    std::vector<std::thread> client_threads_;
    std::mutex client_threads_mutex_;
};

#endif // SOCKET_H
