#ifndef TCP_H
#define TCP_H

#include "utils.h"
#include "ipv4.h"
#include <functional>
#include <map>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <thread>

// TCP constants
constexpr size_t TCP_HEADER_MIN_SIZE = 20;
constexpr size_t TCP_HEADER_MAX_SIZE = 60;
constexpr uint16_t TCP_MIN_PORT = 1;
constexpr uint16_t TCP_MAX_PORT = 65535;
constexpr uint32_t TCP_MAX_WINDOW_SIZE = 65535;
constexpr uint32_t TCP_DEFAULT_MSS = 1460;
constexpr uint32_t TCP_MIN_MSS = 536;

// TCP flags
constexpr uint8_t TCP_FLAG_FIN = 0x01;
constexpr uint8_t TCP_FLAG_SYN = 0x02;
constexpr uint8_t TCP_FLAG_RST = 0x04;
constexpr uint8_t TCP_FLAG_PSH = 0x08;
constexpr uint8_t TCP_FLAG_ACK = 0x10;
constexpr uint8_t TCP_FLAG_URG = 0x20;
constexpr uint8_t TCP_FLAG_ECE = 0x40;
constexpr uint8_t TCP_FLAG_CWR = 0x80;

// TCP options
constexpr uint8_t TCP_OPTION_END = 0;
constexpr uint8_t TCP_OPTION_NOP = 1;
constexpr uint8_t TCP_OPTION_MSS = 2;
constexpr uint8_t TCP_OPTION_WINDOW_SCALE = 3;
constexpr uint8_t TCP_OPTION_SACK_PERMITTED = 4;
constexpr uint8_t TCP_OPTION_TIMESTAMP = 8;

// TCP states
enum class TcpState {
    CLOSED,
    LISTEN,
    SYN_SENT,
    SYN_RECEIVED,
    ESTABLISHED,
    FIN_WAIT_1,
    FIN_WAIT_2,
    CLOSE_WAIT,
    CLOSING,
    LAST_ACK,
    TIME_WAIT
};

// TCP header structure
struct TcpHeader {
    uint16_t source_port;       // Source port
    uint16_t destination_port;  // Destination port
    uint32_t sequence_number;   // Sequence number
    uint32_t acknowledgment_number; // Acknowledgment number
    uint8_t data_offset_reserved; // Data offset (4 bits) + Reserved (3 bits) + NS flag (1 bit)
    uint8_t flags;             // Control flags
    uint16_t window_size;      // Window size
    uint16_t checksum;         // Checksum
    uint16_t urgent_pointer;   // Urgent pointer
    
    TcpHeader();
    TcpHeader(uint16_t src_port, uint16_t dst_port, uint32_t seq, uint32_t ack,
              uint8_t flags, uint16_t window, uint8_t data_offset = 5);
    
    // Header field accessors
    uint8_t get_data_offset() const { return (data_offset_reserved >> 4) & 0x0F; }
    uint8_t get_header_length() const { return get_data_offset() * 4; }
    void set_data_offset(uint8_t offset) { data_offset_reserved = (offset << 4) | (data_offset_reserved & 0x0F); }
    
    // Flag accessors
    bool has_flag(uint8_t flag) const { return (flags & flag) != 0; }
    void set_flag(uint8_t flag) { flags |= flag; }
    void clear_flag(uint8_t flag) { flags &= ~flag; }
    
    bool is_syn() const { return has_flag(TCP_FLAG_SYN); }
    bool is_ack() const { return has_flag(TCP_FLAG_ACK); }
    bool is_fin() const { return has_flag(TCP_FLAG_FIN); }
    bool is_rst() const { return has_flag(TCP_FLAG_RST); }
    bool is_psh() const { return has_flag(TCP_FLAG_PSH); }
    bool is_urg() const { return has_flag(TCP_FLAG_URG); }
    
    // Checksum calculation
    void calculate_checksum(const uint8_t* payload, size_t payload_size,
                           const IpAddress& src_ip, const IpAddress& dst_ip);
    bool verify_checksum(const uint8_t* payload, size_t payload_size,
                        const IpAddress& src_ip, const IpAddress& dst_ip) const;
    
    // Serialization
    void serialize(uint8_t* buffer) const;
    static TcpHeader deserialize(const uint8_t* buffer);
    
    // Validation
    bool is_valid() const;
    
    std::string to_string() const;
} __attribute__((packed));

// TCP options
struct TcpOptions {
    uint16_t mss = TCP_DEFAULT_MSS;
    uint8_t window_scale = 0;
    bool sack_permitted = false;
    bool timestamp_enabled = false;
    uint32_t timestamp_value = 0;
    uint32_t timestamp_echo_reply = 0;
    
    TcpOptions();
    
    // Serialization
    std::vector<uint8_t> serialize() const;
    bool deserialize(const uint8_t* data, size_t size);
    
    // Size calculation
    size_t get_serialized_size() const;
    
    std::string to_string() const;
};

// TCP segment class
class TcpSegment {
public:
    TcpSegment();
    TcpSegment(uint16_t src_port, uint16_t dst_port, uint32_t seq, uint32_t ack,
               uint8_t flags, uint16_t window, const uint8_t* payload = nullptr, size_t payload_size = 0);
    TcpSegment(const TcpHeader& header, const TcpOptions& options = TcpOptions(),
               const uint8_t* payload = nullptr, size_t payload_size = 0);
    
    // Getters
    const TcpHeader& header() const { return header_; }
    const TcpOptions& options() const { return options_; }
    const uint8_t* payload() const { return payload_.data(); }
    size_t payload_size() const { return payload_.size(); }
    size_t total_size() const { return header_.get_header_length() + payload_.size(); }
    
    uint16_t get_source_port() const { return header_.source_port; }
    uint16_t get_destination_port() const { return header_.destination_port; }
    uint32_t get_sequence_number() const { return header_.sequence_number; }
    uint32_t get_acknowledgment_number() const { return header_.acknowledgment_number; }
    uint8_t get_flags() const { return header_.flags; }
    uint16_t get_window_size() const { return header_.window_size; }
    
    // Setters
    void set_source_port(uint16_t port) { header_.source_port = port; }
    void set_destination_port(uint16_t port) { header_.destination_port = port; }
    void set_sequence_number(uint32_t seq) { header_.sequence_number = seq; }
    void set_acknowledgment_number(uint32_t ack) { header_.acknowledgment_number = ack; }
    void set_flags(uint8_t flags) { header_.flags = flags; }
    void set_window_size(uint16_t window) { header_.window_size = window; }
    void set_payload(const uint8_t* data, size_t size);
    void set_options(const TcpOptions& opts);
    
    // Serialization
    std::vector<uint8_t> serialize(const IpAddress& src_ip, const IpAddress& dst_ip) const;
    bool deserialize(const uint8_t* data, size_t size, 
                    const IpAddress& src_ip, const IpAddress& dst_ip);
    
    // Validation
    bool is_valid(const IpAddress& src_ip, const IpAddress& dst_ip) const;
    
    // Sequence number calculations
    uint32_t get_sequence_end() const;
    bool contains_sequence(uint32_t seq) const;
    bool overlaps_with(const TcpSegment& other) const;
    
    // Debug
    std::string to_string() const;
    void dump() const;
    
private:
    TcpHeader header_;
    TcpOptions options_;
    std::vector<uint8_t> payload_;
};

// TCP endpoint
struct TcpEndpoint {
    IpAddress ip;
    uint16_t port;
    
    TcpEndpoint();
    TcpEndpoint(const IpAddress& addr, uint16_t p);
    TcpEndpoint(const std::string& ip_str, uint16_t p);
    
    bool operator==(const TcpEndpoint& other) const;
    bool operator!=(const TcpEndpoint& other) const;
    bool operator<(const TcpEndpoint& other) const;
    
    std::string to_string() const;
    bool is_valid() const;
};

// TCP connection key for identifying connections
struct TcpConnectionKey {
    TcpEndpoint local;
    TcpEndpoint remote;
    
    TcpConnectionKey();
    TcpConnectionKey(const TcpEndpoint& local_ep, const TcpEndpoint& remote_ep);
    
    bool operator==(const TcpConnectionKey& other) const;
    bool operator!=(const TcpConnectionKey& other) const;
    bool operator<(const TcpConnectionKey& other) const;
    
    std::string to_string() const;
};

// Forward declarations
class TcpConnection;
class TcpProtocol;

// TCP socket class
class TcpSocket {
public:
    TcpSocket();
    ~TcpSocket();
    
    // Socket operations
    bool bind(uint16_t port);
    bool bind(const IpAddress& ip, uint16_t port);
    bool listen(int backlog = 5);
    std::shared_ptr<TcpSocket> accept();
    bool connect(const TcpEndpoint& remote);
    void close();
    
    // Data transmission
    ssize_t send(const uint8_t* data, size_t size);
    ssize_t receive(uint8_t* buffer, size_t buffer_size);
    
    // Socket state
    TcpState get_state() const;
    bool is_bound() const;
    bool is_listening() const;
    bool is_connected() const;
    bool is_closed() const;
    
    const TcpEndpoint& get_local_endpoint() const;
    const TcpEndpoint& get_remote_endpoint() const;
    
    // Socket options
    void set_blocking(bool blocking);
    bool is_blocking() const;
    void set_receive_timeout(std::chrono::milliseconds timeout);
    std::chrono::milliseconds get_receive_timeout() const;
    void set_send_timeout(std::chrono::milliseconds timeout);
    std::chrono::milliseconds get_send_timeout() const;
    
    // Buffer management
    void set_receive_buffer_size(size_t size);
    size_t get_receive_buffer_size() const;
    void set_send_buffer_size(size_t size);
    size_t get_send_buffer_size() const;
    size_t get_pending_send_data() const;
    size_t get_pending_receive_data() const;
    
    // Statistics
    struct Statistics {
        uint64_t bytes_sent = 0;
        uint64_t bytes_received = 0;
        uint64_t segments_sent = 0;
        uint64_t segments_received = 0;
        uint64_t retransmissions = 0;
        uint64_t out_of_order_segments = 0;
        uint64_t duplicate_acks = 0;
    };
    
    const Statistics& get_statistics() const;
    void reset_statistics();
    
private:
    friend class TcpConnection;
    friend class TcpProtocol;
    
    std::shared_ptr<TcpConnection> connection_;
    TcpProtocol* protocol_;
};

// Utility functions
std::string tcp_state_to_string(TcpState state);
std::string tcp_flags_to_string(uint8_t flags);
std::string tcp_port_to_string(uint16_t port);
bool is_valid_tcp_port(uint16_t port);
bool is_valid_tcp_segment(const uint8_t* data, size_t size);
uint32_t generate_initial_sequence_number();
bool sequence_less_than(uint32_t a, uint32_t b);
bool sequence_less_equal(uint32_t a, uint32_t b);
uint32_t sequence_add(uint32_t seq, uint32_t offset);
uint32_t sequence_subtract(uint32_t a, uint32_t b);

#endif // TCP_H
